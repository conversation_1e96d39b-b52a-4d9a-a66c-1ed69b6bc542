import React, {
  Fragment,
  useEffect,
  useState,
  useRef,
  useMemo,
  useCallback,
} from "react";
import { useTranslation } from "react-i18next";
import moment from "moment";
import classNames from "classnames";
import { useDispatch, useSelector } from "react-redux";
import { Menu, Spin, Empty } from "antd";
import { cloneDeep } from "lodash";
import { checkRole } from "lib-utils/role-utils";

import { isArray } from "utils";
import { useConfirm, useLoading, useStore, useQueryAll } from "hooks";
import { query } from "redux-store/stores";
import { GlobalStyle, Main } from "./styled";
import { AuthWrapper, Button, Dropdown } from "components";
import { ROLES, THIET_LAP_CHUNG } from "constants/index";
import { SVG } from "assets";
import ModalThemMoiNhanVien from "../components/ModalThemMoiNhanVien";

const getMonthDaysFromRange = (tuThoiGian, denThoiGian) => {
  if (!tuThoiGian || !denThoiGian) return [];

  const start = moment(tuThoiGian).startOf("day");
  const end = moment(denThoiGian).startOf("day");

  const days = [];
  let current = start.clone();
  let counter = 1;

  while (current.isSameOrBefore(end, "day")) {
    days.push({
      label: counter,
      value: current.format("DD/MM/YYYY"),
    });
    current.add(1, "day");
    counter++;
  }

  return days;
};

const DanhSach = ({ parentState, setParentState, khoaLamViec }) => {
  const { showConfirm } = useConfirm();
  const { t } = useTranslation();

  const {
    listData,
    page,
    hasNext,
    isLoading,
    isLoadMore,
    isLoadFinish,
    dataSearch,
  } = useSelector((state) => state.nvThoiGianLamViec);
  const {
    nvThoiGianLamViec: {
      onLoadMoreData,
      searchByParams,
      onDelete: onDeleteItem,
    },
  } = useDispatch();
  const listAllPhong = useStore("phong.listAllPhong", []);
  const { data: listAllNhanVien } = useQueryAll(
    query.nhanVien.queryAllNhanVien({
      params: {
        khoaId: khoaLamViec?.id,
        dsMaThietLapVanBang: [THIET_LAP_CHUNG.BAC_SI],
      },
      enabled: !!khoaLamViec?.id,
    })
  );
  const { showLoading, hideLoading } = useLoading();
  const { tuThoiGian, denThoiGian } = parentState || {};
  const [state, _setState] = useState({
    dataSource: [],
    listActiveKeys: [],
    renderReady: false,
  });
  const refModalThemMoiNhanVien = useRef(null);

  const setState = (data) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const { phongId } = dataSearch || {};
  const rangeDates = getMonthDaysFromRange(tuThoiGian, denThoiGian);

  useEffect(() => {
    if (isLoadFinish) {
      initState({
        listData,
        listAllPhong,
        listAllNhanVien,
        khoaId: khoaLamViec?.id,
        phongId,
      });
    }
  }, [
    isLoadFinish,
    listData,
    listAllPhong,
    listAllNhanVien,
    khoaLamViec?.id,
    phongId,
  ]);

  const initState = (data = {}) => {
    const { listData, listAllPhong, listAllNhanVien, khoaId, phongId } = data;

    const defaultState = {
      dataSource: [],
      listActiveKeys: [],
      renderReady: true,
    };

    if (!isArray(listAllPhong, true) && !isArray(listAllNhanVien, true)) {
      setState({ ...defaultState });
      return;
    }

    const dataByNhanVienIdMap = new Map();
    const hasListData = isArray(listData, true);

    if (hasListData) {
      (listData || []).forEach((item) => {
        const processedItem = {
          ...item,
          thoiGian: moment(item.tuThoiGian).date(),
          listThoiGian: getMonthDaysFromRange(
            item.tuThoiGian,
            item.denThoiGian
          ),
        };
        const nvId = item.nhanVienId;

        if (!dataByNhanVienIdMap.has(nvId)) {
          dataByNhanVienIdMap.set(nvId, []);
        }
        dataByNhanVienIdMap.get(nvId).push(processedItem);
      });
    }

    // map nhân viên theo phòng từ dsKhoa
    const nhanVienByPhongMap = (listAllNhanVien || []).reduce((acc, nv) => {
      const khoaInfo = (nv.dsKhoa || []).find((k) => k.khoaId === khoaId);

      if (khoaInfo) {
        const dsPhongId = khoaInfo.dsPhongId || [];

        dsPhongId.forEach((pId) => {
          if (!acc.has(pId)) {
            acc.set(pId, []);
          }
          acc.get(pId).push(nv);
        });
      }
      return acc;
    }, new Map());

    const filteredPhongList = phongId
      ? listAllPhong.filter((i) => i.id === phongId)
      : listAllPhong;

    const newData = filteredPhongList
      .map((phong) => {
        const nhanVienInPhong = nhanVienByPhongMap.get(phong.id) || [];

        const childrenNhanVien = nhanVienInPhong
          .map((nhanVien) => {
            // lấy data từ listData theo nhân viên
            const nhanVienData = hasListData
              ? (dataByNhanVienIdMap.get(nhanVien.id) || []).filter(
                  (d) => d.phongId === phong.id // chỉ lấy data đúng phòng
                )
              : [];

            return {
              name: nhanVien.ten,
              key: nhanVien.id,
              children: nhanVienData,
            };
          })
          .filter(Boolean);

        return {
          name: phong.ten,
          key: phong.id,
          children: childrenNhanVien,
        };
      })
      .filter((i) => isArray(i.children, true));

    const finalState = {
      dataSource: newData,
      listActiveKeys: newData.map((i) => i.key),
      renderReady: true,
    };

    setState({ ...defaultState, ...finalState });
  };

  const onAddNew = useCallback(
    (item) => () => {
      refModalThemMoiNhanVien.current &&
        refModalThemMoiNhanVien.current.show({
          ...item,
          khoaId: khoaLamViec?.id,
        });
    },
    [khoaLamViec?.id]
  );

  const onEdit = useCallback(
    (item) => () => {
      refModalThemMoiNhanVien.current &&
        refModalThemMoiNhanVien.current.show({
          ...item,
          khoaId: khoaLamViec?.id,
          isEdit: true,
        });
    },
    [khoaLamViec?.id]
  );

  const onDelete = useCallback(
    (item) => () => {
      showConfirm(
        {
          title: t("common.thongBao"),
          content: `${t("quanLyNoiTru.banCoChacMuonXoaNhanVienDaPhanCong")}`,
          cancelText: t("common.huy"),
          okText: t("common.dongY"),
          classNameOkText: "button-warning",
          showImg: true,
          showBtnOk: true,
          typeModal: "error",
        },
        () => {
          showLoading();
          onDeleteItem(item?.id)
            .then((res) => {
              if (res && res.code === 0) {
                searchByParams({});
              }
            })
            .finally(() => {
              hideLoading();
            });
        }
      );
    },
    [t, onDeleteItem, searchByParams]
  );

  const handleNavigate = useCallback(
    (isNext) => () => {
      if (isLoading) return;
      let params = {};
      if (isNext) {
        params = {
          tuThoiGian: moment(tuThoiGian)
            .add(1, "months")
            .format("YYYY/MM/DD 00:00:00"),
          denThoiGian: moment(denThoiGian)
            .add(1, "months")
            .format("YYYY/MM/DD 23:59:59"),
        };
      } else {
        params = {
          tuThoiGian: moment(tuThoiGian)
            .subtract(1, "months")
            .format("YYYY/MM/DD 00:00:00"),
          denThoiGian: moment(denThoiGian)
            .subtract(1, "months")
            .format("YYYY/MM/DD 23:59:59"),
        };
      }
      setParentState(params);
      searchByParams({ ...params });
    },
    [isLoading, tuThoiGian, denThoiGian, setParentState, searchByParams]
  );

  const handleLoadMore = useCallback(() => {
    onLoadMoreData({ page: Number(page) + 1, loadMore: true });
  }, [onLoadMoreData, page]);

  const onCollapse = useCallback(
    (key) => () => {
      let _listKeys = cloneDeep(state.listActiveKeys);
      if (_listKeys.includes(key)) {
        _listKeys = _listKeys.filter((i) => i !== key);
      } else {
        _listKeys.push(key);
      }
      setState({ listActiveKeys: _listKeys });
    },
    [state.listActiveKeys, setState]
  );

  const renderHeader = useMemo(() => {
    return (
      <>
        <colgroup>
          <col
            className="date-col"
            style={{
              width: `calc((100vw / ${rangeDates.length}) + 2px - 50px)`,
            }}
          />
          <col className="action-col" />
          {rangeDates.map((item) => (
            <col
              className="date-col"
              key={item.label}
              style={{
                width: `calc((100vw / ${rangeDates.length}) + 2px - 50px)`,
              }}
            />
          ))}
          <col className="action-col" />
        </colgroup>
        <thead>
          <tr>
            <th className="title sticky-col">
              <p>{t("tiepDon.bacSi")}</p>
            </th>
            <th className="action sticky-col">
              <SVG.IcArrowDown
                rotate={90}
                color="white"
                className="pointer"
                onClick={handleNavigate()}
              />
            </th>
            {rangeDates.map((item) => (
              <th key={item.label}>
                <b>{item.label}</b>
              </th>
            ))}
            <th className="action">
              <SVG.IcArrowDown
                rotate={270}
                color="white"
                className="pointer"
                onClick={handleNavigate(true)}
              />
            </th>
          </tr>
        </thead>
      </>
    );
  }, [rangeDates, handleNavigate]);

  const renderContentItem = useCallback(
    (item) => {
      const { id, tuThoiGian, denThoiGian } = item || {};
      const menu = () => (
        <Menu
          items={[
            {
              key: 1,
              label: <a onClick={onAddNew(item)}>{t("common.themMoi")}</a>,
            },
            {
              key: 2,
              label: <a onClick={onEdit(item)}>{t("common.chinhSua")}</a>,
            },
          ]}
        />
      );

      return (
        <div className="item-content" key={id}>
          <div
            className={classNames("content-action", {
              center: !checkRole([
                ROLES["QUAN_LY_NHAN_LUC"].CRUD_THONG_TIN_LAM_VIEC_NHAN_VIEN,
              ]),
            })}
          >
            <AuthWrapper
              accessRoles={[
                ROLES["QUAN_LY_NHAN_LUC"].CRUD_THONG_TIN_LAM_VIEC_NHAN_VIEN,
              ]}
            >
              <Dropdown
                overlayClassName="dieu-duong-dropdown"
                overlay={menu}
                trigger={"click"}
                placement="right"
                align={{ offset: [0, 25] }}
              >
                <div>
                  <SVG.IcMore className="more-icon pointer" />
                </div>
              </Dropdown>
            </AuthWrapper>
            <div className="times">
              <span>
                {tuThoiGian ? moment(tuThoiGian).format("HH:mm") : ""}
              </span>
              <span>{tuThoiGian && denThoiGian && " - "}</span>
              <span>
                {denThoiGian ? moment(denThoiGian).format("HH:mm") : ""}
              </span>
            </div>
            <AuthWrapper
              accessRoles={[
                ROLES["QUAN_LY_NHAN_LUC"].CRUD_THONG_TIN_LAM_VIEC_NHAN_VIEN,
              ]}
            >
              <SVG.IcCancel className="pointer" onClick={onDelete(item)} />
            </AuthWrapper>
          </div>
        </div>
      );
    },
    [t, onAddNew, onEdit, onDelete]
  );

  const renderContent = useMemo(() => {
    return (
      <tbody>
        {state.dataSource.map((item) => {
          const activeKey = state.listActiveKeys.includes(item.key);
          return (
            <Fragment key={item.key}>
              <tr className={"row-collapse"}>
                <td className="sticky-col">
                  <div className="flex">
                    <SVG.IcExpandRight
                      className={classNames("collapse-icon", {
                        active: activeKey,
                      })}
                      onClick={onCollapse(item.key)}
                    />
                    <span>{item.name.toUpperCase()}</span>
                  </div>
                </td>
                <td className="sticky-col" />
                {rangeDates.map((i, idx) => (
                  <td key={idx}>
                    <AuthWrapper
                      accessRoles={[
                        ROLES["QUAN_LY_NHAN_LUC"]
                          .CRUD_THONG_TIN_LAM_VIEC_NHAN_VIEN,
                      ]}
                    >
                      <div className="flex-center">
                        <SVG.IcPlus
                          className="pointer"
                          onClick={onAddNew({ ...item, time: i.label })}
                        />
                      </div>
                    </AuthWrapper>
                  </td>
                ))}
                <td />
              </tr>
              {activeKey &&
                (item.children || []).map((i) => {
                  return (
                    <tr key={i.key}>
                      <td className="sticky-col">
                        <div>{i.name}</div>
                      </td>
                      <td className="sticky-col" />
                      {rangeDates.map((j, idx) => {
                        let level2Items = (i.children || []).filter((k) =>
                          k.listThoiGian.some((l) => l.label === j.label)
                        );
                        return (
                          <td key={j + idx}>
                            <div
                              className={classNames("item", {
                                padding: isArray(level2Items, true),
                              })}
                            >
                              {isArray(level2Items, true) ? (
                                level2Items
                                  .sort((a, b) => {
                                    let tuA = new Date(a.tuThoiGian).valueOf(),
                                      tuB = new Date(b.tuThoiGian).valueOf(),
                                      denA = new Date(a.denThoiGian).valueOf(),
                                      denB = new Date(b.denThoiGian).valueOf();
                                    if (tuA === tuB) {
                                      return denA - denB;
                                    } else {
                                      return tuA - tuB;
                                    }
                                  })
                                  .map(renderContentItem)
                              ) : (
                                <AuthWrapper
                                  accessRoles={[
                                    ROLES["QUAN_LY_NHAN_LUC"]
                                      .CRUD_THONG_TIN_LAM_VIEC_NHAN_VIEN,
                                  ]}
                                >
                                  <div
                                    className="item-add"
                                    onClick={onAddNew({
                                      nhanVienId: i.key,
                                      phongId: item.key,
                                      time: j.label,
                                    })}
                                  >
                                    <SVG.IcAdd />
                                  </div>
                                </AuthWrapper>
                              )}
                            </div>
                          </td>
                        );
                      })}
                      <td />
                    </tr>
                  );
                })}
            </Fragment>
          );
        })}
      </tbody>
    );
  }, [
    state.dataSource,
    state.listActiveKeys,
    rangeDates,
    onAddNew,
    onCollapse,
    renderContentItem,
  ]);

  const renderLoading = useMemo(() => {
    return (
      <div className="loading">
        <Spin size="large" />
      </div>
    );
  }, []);

  const renderNotFound = useMemo(() => {
    return (
      <tbody>
        <tr>
          <td colSpan={`${rangeDates?.length + 3}`} className="not-found">
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
          </td>
        </tr>
      </tbody>
    );
  }, [rangeDates]);

  const renderFooter = useMemo(() => {
    return (
      <div className="footer">
        {!isLoadMore && hasNext && (
          <Button onClick={handleLoadMore}>{t("common.xemThem")}</Button>
        )}
        {isLoadMore && <Spin />}
      </div>
    );
  }, [hasNext, isLoadMore, handleLoadMore]);

  return (
    <>
      <GlobalStyle />
      <Main noPadding={true}>
        <div className="table-wrapper">
          <table>
            {renderHeader}
            {isArray(state.dataSource, true) && renderContent}
            {!isArray(state.dataSource, true) && !isLoading && renderNotFound}
          </table>
          {renderFooter}
        </div>
        {(isLoading || !state.renderReady) && !isLoadMore && renderLoading}
        <ModalThemMoiNhanVien
          ref={refModalThemMoiNhanVien}
          rangeDates={rangeDates}
        />
      </Main>
    </>
  );
};

export default DanhSach;
