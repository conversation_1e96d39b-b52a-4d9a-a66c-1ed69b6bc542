import React, {
  Fragment,
  useEffect,
  useState,
  useRef,
  useMemo,
  useCallback,
} from "react";
import { useTranslation } from "react-i18next";
import moment from "moment";
import classNames from "classnames";
import { useDispatch, useSelector } from "react-redux";
import { Menu, Spin, Empty } from "antd";
import { cloneDeep } from "lodash";
import { checkRole } from "lib-utils/role-utils";

import { isArray } from "utils";
import { useConfirm, useLoading, useStore, useQueryAll } from "hooks";
import { query } from "redux-store/stores";
import { GlobalStyle, Main } from "./styled";
import { AuthWrapper, Button, Dropdown } from "components";
import { ROLES, THIET_LAP_CHUNG } from "constants/index";
import { SVG } from "assets";
import ModalThemMoiNhanVien from "../components/ModalThemMoiNhanVien";

// Memoized function to avoid recalculation
const getMonthDaysFromRange = (() => {
  const cache = new Map();

  return (tuThoiGian, denThoiGian) => {
    if (!tuThoiGian || !denThoiGian) return [];

    const cacheKey = `${tuThoiGian}-${denThoiGian}`;
    if (cache.has(cacheKey)) {
      return cache.get(cacheKey);
    }

    const start = moment(tuThoiGian).startOf("day");
    const end = moment(denThoiGian).startOf("day");

    const days = [];
    let current = start.clone();
    let counter = 1;

    while (current.isSameOrBefore(end, "day")) {
      days.push({
        label: counter,
        value: current.format("DD/MM/YYYY"),
      });
      current.add(1, "day");
      counter++;
    }

    cache.set(cacheKey, days);

    // Clear cache if it gets too large
    if (cache.size > 50) {
      const firstKey = cache.keys().next().value;
      cache.delete(firstKey);
    }

    return days;
  };
})();

/**
 * Custom hook for intersection observer
 * Used for lazy loading of table rows to improve performance
 */
const useIntersectionObserver = (ref, options = {}) => {
  const [isIntersecting, setIsIntersecting] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(([entry]) => {
      setIsIntersecting(entry.isIntersecting);
    }, options);

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => {
      observer.disconnect();
    };
  }, [ref, options]);

  return isIntersecting;
};

const DanhSach = ({ parentState, setParentState, khoaLamViec }) => {
  const { showConfirm } = useConfirm();
  const { t } = useTranslation();

  const {
    listData,
    page,
    hasNext,
    isLoading,
    isLoadMore,
    isLoadFinish,
    dataSearch,
  } = useSelector((state) => state.nvThoiGianLamViec);
  const {
    nvThoiGianLamViec: {
      onLoadMoreData,
      searchByParams,
      onDelete: onDeleteItem,
    },
  } = useDispatch();
  const listAllPhong = useStore("phong.listAllPhong", []);
  const { data: listAllNhanVien } = useQueryAll(
    query.nhanVien.queryAllNhanVien({
      params: {
        khoaId: khoaLamViec?.id,
        dsMaThietLapVanBang: [THIET_LAP_CHUNG.BAC_SI],
      },
      enabled: !!khoaLamViec?.id,
    })
  );
  const { showLoading, hideLoading } = useLoading();
  const { tuThoiGian, denThoiGian } = parentState || {};
  const [state, _setState] = useState({
    dataSource: [],
    listActiveKeys: [],
    renderReady: false,
    isProcessing: false,
  });
  const refModalThemMoiNhanVien = useRef(null);

  const setState = useCallback((data) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  }, []);

  const { phongId, dsNhanVienId } = dataSearch || {};

  const rangeDates = useMemo(
    () => getMonthDaysFromRange(tuThoiGian, denThoiGian),
    [tuThoiGian, denThoiGian]
  );

  const initState = useCallback(
    (data = {}) => {
      const {
        listData,
        listAllPhong,
        listAllNhanVien,
        khoaId,
        phongId,
        dsNhanVienId,
      } = data;

      // Set processing state immediately
      setState({ isProcessing: true, renderReady: false });

      const defaultState = {
        dataSource: [],
        listActiveKeys: [],
        renderReady: true,
        isProcessing: false,
      };

      // Early return if no data or still loading employees
      if (!isArray(listAllPhong, true) || !isArray(listAllNhanVien, true)) {
        // If we have rooms but no employees yet, keep loading state
        if (isArray(listAllPhong, true) && !isArray(listAllNhanVien, true)) {
          setState({
            dataSource: [],
            listActiveKeys: [],
            renderReady: false,
            isProcessing: true,
          });
          return;
        }
        setState(defaultState);
        return;
      }

      // Use requestIdleCallback for non-blocking processing
      const processData = () => {
        try {
          // Pre-process data maps for better performance
          const dataByNhanVienPhongMap = new Map(); // Combined key: nhanVienId-phongId
          const hasListData = isArray(listData, true);

          if (hasListData) {
            listData.forEach((item) => {
              const key = `${item.nhanVienId}-${item.phongId}`;
              if (!dataByNhanVienPhongMap.has(key)) {
                dataByNhanVienPhongMap.set(key, []);
              }

              // Pre-calculate expensive operations
              const processedItem = {
                ...item,
                thoiGian: moment(item.tuThoiGian).date(),
                listThoiGian: getMonthDaysFromRange(
                  item.tuThoiGian,
                  item.denThoiGian
                ),
              };

              dataByNhanVienPhongMap.get(key).push(processedItem);
            });
          }

          // Build employee-room mapping more efficiently
          const nhanVienByPhongMap = new Map();
          if (isArray(listAllNhanVien, true)) {
            // Filter employees by dsNhanVienId if provided
            const filteredNhanVien = isArray(dsNhanVienId, true)
              ? listAllNhanVien.filter((nv) => dsNhanVienId.includes(nv.id))
              : listAllNhanVien;

            filteredNhanVien.forEach((nv) => {
              const khoaInfo = nv.dsKhoa?.find((k) => k.khoaId === khoaId);
              if (khoaInfo?.dsPhongId) {
                khoaInfo.dsPhongId.forEach((pId) => {
                  if (!nhanVienByPhongMap.has(pId)) {
                    nhanVienByPhongMap.set(pId, []);
                  }
                  nhanVienByPhongMap.get(pId).push(nv);
                });
              }
            });
          }

          // Filter rooms early
          const filteredPhongList = phongId
            ? listAllPhong.filter((i) => i.id === phongId)
            : listAllPhong;

          // Build final data structure
          const newData = [];
          const activeKeys = [];

          filteredPhongList.forEach((phong) => {
            const nhanVienInPhong = nhanVienByPhongMap.get(phong.id);
            if (!nhanVienInPhong?.length) return;

            const childrenNhanVien = nhanVienInPhong.map((nhanVien) => {
              const dataKey = `${nhanVien.id}-${phong.id}`;
              const nhanVienData = dataByNhanVienPhongMap.get(dataKey) || [];

              return {
                name: nhanVien.ten,
                key: nhanVien.id,
                children: nhanVienData,
              };
            });

            if (childrenNhanVien.length > 0) {
              newData.push({
                name: phong.ten,
                key: phong.id,
                children: childrenNhanVien,
              });
              activeKeys.push(phong.id);
            }
          });

          // Update state with processed data
          setState({
            dataSource: newData,
            listActiveKeys: activeKeys,
            renderReady: true,
            isProcessing: false,
          });
        } catch (error) {
          console.error("Error processing data:", error);
          setState({ ...defaultState, renderReady: true, isProcessing: false });
        }
      };

      // Use requestIdleCallback for better performance, fallback to setTimeout
      if (window.requestIdleCallback) {
        window.requestIdleCallback(processData, { timeout: 100 });
      } else {
        setTimeout(processData, 0);
      }
    },
    [] // Empty dependencies to prevent recreation
  );

  useEffect(() => {
    if (isLoadFinish) {
      initState({
        listData,
        listAllPhong,
        listAllNhanVien,
        khoaId: khoaLamViec?.id,
        phongId,
        dsNhanVienId,
      });
    }
  }, [
    isLoadFinish,
    listData,
    listAllPhong,
    listAllNhanVien,
    khoaLamViec?.id,
    phongId,
    dsNhanVienId,
    initState,
  ]);

  const onAddNew = useCallback(
    (item) => () => {
      refModalThemMoiNhanVien.current &&
        refModalThemMoiNhanVien.current.show({
          ...item,
          khoaId: khoaLamViec?.id,
        });
    },
    [khoaLamViec?.id]
  );

  const onEdit = useCallback(
    (item) => () => {
      refModalThemMoiNhanVien.current &&
        refModalThemMoiNhanVien.current.show({
          ...item,
          khoaId: khoaLamViec?.id,
          isEdit: true,
        });
    },
    [khoaLamViec?.id]
  );

  const onDelete = useCallback(
    (item) => () => {
      showConfirm(
        {
          title: t("common.thongBao"),
          content: `${t("quanLyNoiTru.banCoChacMuonXoaNhanVienDaPhanCong")}`,
          cancelText: t("common.huy"),
          okText: t("common.dongY"),
          classNameOkText: "button-warning",
          showImg: true,
          showBtnOk: true,
          typeModal: "error",
        },
        () => {
          showLoading();
          onDeleteItem(item?.id)
            .then((res) => {
              if (res && res.code === 0) {
                searchByParams({});
              }
            })
            .finally(() => {
              hideLoading();
            });
        }
      );
    },
    [t, onDeleteItem, searchByParams]
  );

  const handleNavigate = useCallback(
    (isNext) => () => {
      if (isLoading) return;
      let params = {};
      if (isNext) {
        params = {
          tuThoiGian: moment(tuThoiGian)
            .add(1, "months")
            .format("YYYY/MM/DD 00:00:00"),
          denThoiGian: moment(denThoiGian)
            .add(1, "months")
            .format("YYYY/MM/DD 23:59:59"),
        };
      } else {
        params = {
          tuThoiGian: moment(tuThoiGian)
            .subtract(1, "months")
            .format("YYYY/MM/DD 00:00:00"),
          denThoiGian: moment(denThoiGian)
            .subtract(1, "months")
            .format("YYYY/MM/DD 23:59:59"),
        };
      }
      setParentState(params);
      searchByParams({ ...params });
    },
    [isLoading, tuThoiGian, denThoiGian, setParentState, searchByParams]
  );

  const handleLoadMore = useCallback(() => {
    onLoadMoreData({ page: Number(page) + 1, loadMore: true });
  }, [onLoadMoreData, page]);

  const onCollapse = useCallback(
    (key) => () => {
      let _listKeys = cloneDeep(state.listActiveKeys);
      if (_listKeys.includes(key)) {
        _listKeys = _listKeys.filter((i) => i !== key);
      } else {
        _listKeys.push(key);
      }
      setState({ listActiveKeys: _listKeys });
    },
    [state.listActiveKeys, setState]
  );

  const renderHeader = useMemo(() => {
    return (
      <>
        <colgroup>
          <col
            className="date-col"
            style={{
              width: `calc((100vw / ${rangeDates.length}) + 2px - 50px)`,
            }}
          />
          <col className="action-col" />
          {rangeDates.map((item) => (
            <col
              className="date-col"
              key={item.label}
              style={{
                width: `calc((100vw / ${rangeDates.length}) + 2px - 50px)`,
              }}
            />
          ))}
          <col className="action-col" />
        </colgroup>
        <thead>
          <tr>
            <th className="title sticky-col">
              <p>{t("tiepDon.bacSi")}</p>
            </th>
            <th className="action">
              <SVG.IcArrowDown
                rotate={90}
                color="white"
                className="pointer"
                onClick={handleNavigate()}
              />
            </th>
            {rangeDates.map((item) => (
              <th key={item.label}>
                <b>{item.label}</b>
              </th>
            ))}
            <th className="action">
              <SVG.IcArrowDown
                rotate={270}
                color="white"
                className="pointer"
                onClick={handleNavigate(true)}
              />
            </th>
          </tr>
        </thead>
      </>
    );
  }, [rangeDates, handleNavigate]);

  // Memoized content item component for better performance
  const ContentItem = React.memo(({ item, onAddNew, onEdit, onDelete, t }) => {
    const { id, tuThoiGian, denThoiGian } = item || {};

    const menu = useMemo(
      () => (
        <Menu
          items={[
            {
              key: 1,
              label: <a onClick={onAddNew(item)}>{t("common.themMoi")}</a>,
            },
            {
              key: 2,
              label: <a onClick={onEdit(item)}>{t("common.chinhSua")}</a>,
            },
          ]}
        />
      ),
      [item, onAddNew, onEdit, t]
    );

    const hasRole = useMemo(
      () =>
        checkRole([
          ROLES["QUAN_LY_NHAN_LUC"].CRUD_THONG_TIN_LAM_VIEC_NHAN_VIEN,
        ]),
      []
    );

    const timeDisplay = useMemo(
      () => ({
        start: tuThoiGian ? moment(tuThoiGian).format("HH:mm") : "",
        end: denThoiGian ? moment(denThoiGian).format("HH:mm") : "",
        separator: tuThoiGian && denThoiGian ? " - " : "",
      }),
      [tuThoiGian, denThoiGian]
    );

    return (
      <div className="item-content" key={id}>
        <div
          className={classNames("content-action", {
            center: !hasRole,
          })}
        >
          <AuthWrapper
            accessRoles={[
              ROLES["QUAN_LY_NHAN_LUC"].CRUD_THONG_TIN_LAM_VIEC_NHAN_VIEN,
            ]}
          >
            <Dropdown
              overlayClassName="dieu-duong-dropdown"
              overlay={menu}
              trigger={"click"}
              placement="right"
              align={{ offset: [0, 25] }}
            >
              <div>
                <SVG.IcMore className="more-icon pointer" />
              </div>
            </Dropdown>
          </AuthWrapper>
          <div className="times">
            <span>{timeDisplay.start}</span>
            <span>{timeDisplay.separator}</span>
            <span>{timeDisplay.end}</span>
          </div>
          <AuthWrapper
            accessRoles={[
              ROLES["QUAN_LY_NHAN_LUC"].CRUD_THONG_TIN_LAM_VIEC_NHAN_VIEN,
            ]}
          >
            <SVG.IcCancel className="pointer" onClick={onDelete(item)} />
          </AuthWrapper>
        </div>
      </div>
    );
  });

  const renderContentItem = useCallback(
    (item) => (
      <ContentItem
        key={item.id}
        item={item}
        onAddNew={onAddNew}
        onEdit={onEdit}
        onDelete={onDelete}
        t={t}
      />
    ),
    [onAddNew, onEdit, onDelete, t]
  );

  // Lazy loading row component with intersection observer
  const LazyPhongRow = React.memo(
    ({
      item,
      activeKey,
      rangeDates,
      onCollapse,
      onAddNew,
      renderContentItem,
      index,
    }) => {
      const rowRef = useRef(null);
      const isVisible = useIntersectionObserver(rowRef, {
        threshold: 0.1,
        rootMargin: "100px",
      });
      const [hasBeenVisible, setHasBeenVisible] = useState(false);

      useEffect(() => {
        if (isVisible && !hasBeenVisible) {
          setHasBeenVisible(true);
        }
      }, [isVisible, hasBeenVisible]);

      // Always render first few items immediately
      const shouldRender = index < 5 || hasBeenVisible || isVisible;

      return (
        <Fragment key={item.key}>
          <tr ref={rowRef} className={"row-collapse"}>
            <td className="sticky-col">
              <div className="flex">
                <SVG.IcExpandRight
                  className={classNames("collapse-icon", {
                    active: activeKey,
                  })}
                  onClick={onCollapse(item.key)}
                />
                <span>{item.name.toUpperCase()}</span>
              </div>
            </td>
            <td className="sticky-col" />
            {shouldRender
              ? rangeDates.map((i, idx) => (
                  <td key={idx}>
                    <AuthWrapper
                      accessRoles={[
                        ROLES["QUAN_LY_NHAN_LUC"]
                          .CRUD_THONG_TIN_LAM_VIEC_NHAN_VIEN,
                      ]}
                    >
                      <div className="flex-center">
                        <SVG.IcPlus
                          className="pointer"
                          onClick={onAddNew({ ...item, time: i.label })}
                        />
                      </div>
                    </AuthWrapper>
                  </td>
                ))
              : // Render placeholder cells for better performance
                rangeDates.map((_, idx) => <td key={idx}></td>)}
            <td />
          </tr>
          {activeKey &&
            shouldRender &&
            (item.children || []).map((i) => (
              <NhanVienRow
                key={i.key}
                nhanVien={i}
                phongKey={item.key}
                rangeDates={rangeDates}
                onAddNew={onAddNew}
                renderContentItem={renderContentItem}
              />
            ))}
        </Fragment>
      );
    }
  );

  // Keep the original PhongRow for backward compatibility
  const PhongRow = LazyPhongRow;

  // Memoized employee row component
  const NhanVienRow = React.memo(
    ({ nhanVien, phongKey, rangeDates, onAddNew, renderContentItem }) => {
      const sortedItems = useMemo(() => {
        const sortFn = (a, b) => {
          const tuA = new Date(a.tuThoiGian).valueOf();
          const tuB = new Date(b.tuThoiGian).valueOf();
          const denA = new Date(a.denThoiGian).valueOf();
          const denB = new Date(b.denThoiGian).valueOf();
          return tuA === tuB ? denA - denB : tuA - tuB;
        };
        return (nhanVien.children || []).sort(sortFn);
      }, [nhanVien.children]);

      return (
        <tr key={nhanVien.key}>
          <td className="sticky-col">
            <div>{nhanVien.name}</div>
          </td>
          <td className="sticky-col-second" />
          {rangeDates.map((j, idx) => {
            const level2Items = sortedItems.filter((k) =>
              k.listThoiGian.some((l) => l.label === j.label)
            );

            return (
              <td key={`${j.label}-${idx}`}>
                <div
                  className={classNames("item", {
                    padding: isArray(level2Items, true),
                  })}
                >
                  {isArray(level2Items, true) ? (
                    level2Items.map(renderContentItem)
                  ) : (
                    <AuthWrapper
                      accessRoles={[
                        ROLES["QUAN_LY_NHAN_LUC"]
                          .CRUD_THONG_TIN_LAM_VIEC_NHAN_VIEN,
                      ]}
                    >
                      <div
                        className="item-add"
                        onClick={onAddNew({
                          nhanVienId: nhanVien.key,
                          phongId: phongKey,
                          time: j.label,
                        })}
                      >
                        <SVG.IcAdd />
                      </div>
                    </AuthWrapper>
                  )}
                </div>
              </td>
            );
          })}
          <td />
        </tr>
      );
    }
  );

  const renderContent = useMemo(() => {
    if (!state.renderReady) return null;

    return (
      <tbody>
        {state.dataSource.map((item, index) => {
          const activeKey = state.listActiveKeys.includes(item.key);
          return (
            <PhongRow
              key={item.key}
              item={item}
              activeKey={activeKey}
              rangeDates={rangeDates}
              onCollapse={onCollapse}
              onAddNew={onAddNew}
              renderContentItem={renderContentItem}
              index={index}
            />
          );
        })}
      </tbody>
    );
  }, [
    state.dataSource,
    state.listActiveKeys,
    state.renderReady,
    rangeDates,
    onAddNew,
    onCollapse,
    renderContentItem,
  ]);

  const renderLoading = useMemo(() => {
    return (
      <div className="loading">
        <Spin size="large" />
      </div>
    );
  }, []);

  const renderNotFound = useMemo(() => {
    return (
      <tbody>
        <tr>
          <td colSpan={`${rangeDates?.length + 3}`} className="not-found">
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
          </td>
        </tr>
      </tbody>
    );
  }, [rangeDates?.length]);

  const renderFooter = useMemo(() => {
    return (
      <div className="footer">
        {!isLoadMore && hasNext && (
          <Button onClick={handleLoadMore}>{t("common.xemThem")}</Button>
        )}
        {isLoadMore && <Spin />}
      </div>
    );
  }, [hasNext, isLoadMore, handleLoadMore, t]);

  return (
    <>
      <GlobalStyle />
      <Main noPadding={true}>
        <div className="table-wrapper">
          <table>
            {renderHeader}
            {state.renderReady &&
              isArray(state.dataSource, true) &&
              renderContent}
            {state.renderReady &&
              !isArray(state.dataSource, true) &&
              !isLoading &&
              renderNotFound}
          </table>
          {state.renderReady && renderFooter}
        </div>
        {(isLoading || !state.renderReady || state.isProcessing) &&
          !isLoadMore &&
          renderLoading}
        <ModalThemMoiNhanVien
          ref={refModalThemMoiNhanVien}
          rangeDates={rangeDates}
        />
      </Main>
    </>
  );
};

export default DanhSach;
