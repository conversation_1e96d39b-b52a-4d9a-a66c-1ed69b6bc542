import styled, { createGlobalStyle } from "styled-components";
import { Card } from "components";

export const Main = styled(Card)`
  flex: 1;
  display: flex;
  flex-direction: column;
  color: #03317b;
  position: relative;
  .table-wrapper {
    overflow-y: auto;
    overflow-y: overlay;
    height: 100%;
  }
  .pointer {
    cursor: pointer;
  }
  .more-icon {
    transform: scale(1.5);
  }

  .loading {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 0;
    background-color: white;
    opacity: 0.8;
    z-index: 10;
    backdrop-filter: blur(2px);
  }
  .not-found {
    padding: 16px 24px;
  }
  .footer {
    display: flex;
    justify-content: center;
    margin-top: 8px;
  }
  table {
    width: 100vw;
    max-width: 100%;
    border-collapse: collapse;
    word-break: break-word;
    .sticky-col {
      font-weight: 700;
      text-align: left;
      padding-left: 8px;
      position: sticky;
      left: 0;
      background: white;
      z-index: 2;
      border-right: 2px solid #ddd;
      box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
      .flex {
        align-items: center;
      }
    }
    .sticky-col-second {
      position: sticky;
      left: 120px; /* Width of first sticky column */
      background: white;
      z-index: 2;
      font-weight: 700;
      text-align: center;
      padding: 0;
      border-right: 2px solid #ddd;
      box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
    }
    thead {
      position: -webkit-sticky;
      position: sticky;
      top: 0;
      z-index: 3;
      th.sticky-col {
        z-index: 4;
        background: #1963d1 !important;
        color: white;
        border-right: 2px solid #0d47a1;
      }
      th.sticky-col-second {
        z-index: 4;
        background: #1963d1 !important;
        color: white;
        border-right: 2px solid #0d47a1;
      }
    }
    tbody {
      overflow: auto;
    }
    th,
    td {
      border-collapse: collapse;
      border: 1px solid #e2e4e8;
      overflow: hidden;
    }
    tr {
      overflow: hidden;
      &.row-collapse {
        background: #c1f0db;
        height: 32px;
        .collapse-icon {
          transition: all 0.25s;
          &.active {
            transform: rotate(0deg);
          }
        }
        span {
          font-weight: 700;
        }
      }
    }
    th {
      background: #1963d1;
      color: white;
      min-width: 140px;
      border-top: none;
      &.action {
        min-width: 24px;
        max-width: 24px;
        width: 24px;
        &.sticky-col {
          padding-left: 0;
          text-align: center;
        }
      }
      &.title {
        min-width: 120px;
        p {
          text-align: left;
        }
      }
      b {
        font-size: 16px;
        text-align: center;
        display: inline-block;
        width: 100%;
      }
      p {
        margin-bottom: 0;
        font-weight: normal;
        text-align: center;
      }
    }
    td {
      text-align: center;
      vertical-align: center;
      height: 0;
      & > * {
        min-height: 32px;
      }
      span {
        margin-left: 8px;
        display: inline-block;
      }
      & div.item {
        vertical-align: top;
        height: 100%;
        &.padding {
          padding: 4px;
        }
        .item-add {
          display: flex;
          justify-content: center;
          align-items: center;
          opacity: 0;
          visibility: hidden;
          transition: all 0.25s;
          cursor: pointer;
          width: 100%;
          height: 100%;
        }
        &:hover .item-add {
          opacity: 1;
          visibility: visible;
        }
        .item-content {
          background: #e6effe;
          padding: 2px;
          margin-bottom: 8px;
          border-radius: 4px;
          color: #03317b;
          .times {
            display: flex;
            gap: 2px;
            flex-wrap: wrap;
            span {
              margin-left: 0px !important;
            }
          }
          &:last-child {
            margin-bottom: 0px;
          }
          b {
            font-size: 16px;
          }
          .content-action {
            & span {
              margin-left: 0px !important;
            }
            &.center {
              justify-content: center;
            }
            display: flex;
            justify-content: space-between;
          }
        }
      }
    }
  }
`;

export const GlobalStyle = createGlobalStyle`
  & .dieu-duong-dropdown{
    & .ant-dropdown-menu {
      border-radius: 8px !important;
      & .ant-dropdown-menu-item:not(:last-child) {
        border-bottom: 1px solid #d9d9d9;
      }
    }
  }
`;
