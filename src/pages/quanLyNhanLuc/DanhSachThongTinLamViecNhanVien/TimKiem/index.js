import React, { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { DatePicker } from "antd";

import { useQueryAll, useStore } from "hooks";
import { getAllQueryString } from "hooks/useQueryString/queryString";

import { BaseSearch } from "components";
import { selectMaTen } from "redux-store/selectors";
import { query } from "redux-store/stores";
import { THIET_LAP_CHUNG } from "constants/index";
import { GlobalStyle } from "./styled";

const TimKiem = ({ khoaLamViec, parentState, setParentState }) => {
  const { t } = useTranslation();
  const listAllPhong = useStore("phong.listAllPhong", []);
  const { tuThoiGian, denThoiGian } = parentState || {};

  const [state, _setState] = useState({});

  const setState = (data) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const {
    nvThoiGianLamViec: { searchByParams, clearData },
    phong: { getListAllPhong },
  } = useDispatch();

  useEffect(() => {
    if (khoaLamViec?.id) {
      const { page, size, dataSortColumn, ...queries } = getAllQueryString();
      if (queries.phongId) queries.phongId = Number(queries.phongId);
      if (queries.giuongId) queries.giuongId = Number(queries.giuongId);
      setState(queries);
      searchByParams({
        page: parseInt(page || 0),
        size: parseInt(size || 200),
        tuThoiGian,
        denThoiGian,
        khoaId: khoaLamViec.id,
        ...queries,
      });
      getListAllPhong({
        page: "",
        size: "",
        active: true,
        dsKhoaId: khoaLamViec.id,
        dsLoaiPhong: [50, 60],
      });
    }
  }, [khoaLamViec?.id]);

  useEffect(() => {
    if (tuThoiGian) {
      setState({ thoiGian: moment(tuThoiGian, "YYYY-MM-DD HH:mm:ss") });
    }
  }, [tuThoiGian]);

  const { data: listAllNhanVien } = useQueryAll(
    query.nhanVien.queryAllNhanVien({
      params: {
        khoaId: khoaLamViec?.id,
        dsMaThietLapVanBang: [THIET_LAP_CHUNG.BAC_SI],
      },
      enabled: !!khoaLamViec?.id,
    })
  );

  useEffect(() => {
    return () => {
      clearData({});
      _setState({});
    };
  }, []);

  const onChangeInputSearch = (e) => {
    let data = e;
    searchByParams(data);
    setState(data);
  };

  const onChangeTime = (data) => {
    let thoiGian = moment(data.thoiGian, "YYYY-MM-DD HH:mm:ss");
    setState({ thoiGian });
    let tuThoiGian = thoiGian.startOf("month").format("YYYY-MM-DD HH:mm:ss");
    let denThoiGian = thoiGian.endOf("month").format("YYYY-MM-DD HH:mm:ss");
    setParentState({ tuThoiGian, denThoiGian });
    searchByParams({ tuThoiGian, denThoiGian });
  };

  return (
    <>
      <GlobalStyle />
      <BaseSearch
        cacheData={state}
        dataInput={[
          {
            widthInput: "120px",
            placeholder: t("common.phong"),
            keyValueInput: "phongId",
            functionChangeInput: onChangeInputSearch,
            type: "select",
            listSelect: listAllPhong,
          },
          {
            widthInput: "300px",
            placeholder: t("baoCao.tenBacSi"),
            keyValueInput: "dsNhanVienId",
            functionChangeInput: onChangeInputSearch,
            type: "select",
            mode: "multiple",
            listSelect: listAllNhanVien,
            getLabel: selectMaTen,
          },
          {
            widthInput: "250px",
            type: "date",
            picker: "month",
            functionChangeInput: onChangeTime,
            putToQuerry: false,
            keyValueInput: "thoiGian",
            placeholder: t("quanLyNhanLuc.chonThoiGianLamViec"),
            format: "MM-YYYY",
            value: state.thoiGian,
            allowClear: false,
            popupClassName: "custom-date-picker",
          },
        ]}
        filter={{
          open: true,
          width: "110px",
          funcSearchData: onChangeInputSearch,
          popoverWidth: "250px",
          data: [],
        }}
      />
    </>
  );
};

export default TimKiem;
