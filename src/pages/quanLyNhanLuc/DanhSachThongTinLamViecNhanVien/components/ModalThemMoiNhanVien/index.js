import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
  useMemo,
} from "react";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { Form, TimePicker } from "antd";
import { Button, ModalTemplate, Select, DatePicker } from "components";
import { HOTKEY, THIET_LAP_CHUNG } from "constants/index";
import { useLoading, useQueryAll, useStore } from "hooks";
import { isArray } from "utils/index";
import { capitalize } from "lodash";
import { SVG } from "assets";
import { Main } from "./styled";
import { query } from "redux-store/stores";
import { selectMaTen } from "redux-store/selectors";

const { RangePicker } = DatePicker;

const ModalThemMoiNhanVien = ({ rangeDates }, ref) => {
  const { t } = useTranslation();
  const refModal = useRef(null);
  const [state, _setState] = useState({ show: false });
  const [form] = Form.useForm();
  const ngayLamViec = Form.useWatch("ngayLamViec", form);
  const listAllPhong = useStore("phong.listAllPhong", []);
  const phongIdForm = Form.useWatch("phongId", form);

  const { showLoading, hideLoading } = useLoading();

  const {
    nvThoiGianLamViec: { searchByParams, crudNvThoiGianLamViec },
  } = useDispatch();

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  useImperativeHandle(ref, () => ({
    show: (data) => {
      const {
        khoaId,
        key,
        phongId,
        time,
        thoiGian: _thoiGian,
        isEdit,
        nhanVienId,
        tuThoiGian,
        denThoiGian,
        id,
      } = data || {};
      let thoiGian = rangeDates.find(
        (i) => i.label === (time || _thoiGian)
      )?.value;

      setState({ show: true, khoaId, isEdit, id });
      form.setFieldsValue({
        phongId: key || phongId,
        nhanVienId,
        thoiGianLamViec:
          isEdit && tuThoiGian && denThoiGian
            ? [
                moment(tuThoiGian.toDateObject(), "HH:mm:ss"),
                moment(denThoiGian.toDateObject(), "HH:mm:ss"),
              ]
            : null,
        ...(thoiGian && {
          ngayLamViec: [
            moment(thoiGian, "DD/MM/YYYY"),
            moment(thoiGian, "DD/MM/YYYY"),
          ],
        }),
      });
    },
  }));

  const { data: listAllNhanVien } = useQueryAll(
    query.nhanVien.queryAllNhanVien({
      params: {
        khoaId: state.khoaId,
        dsMaThietLapVanBang: [THIET_LAP_CHUNG.BAC_SI],
      },
      enabled: !!state.khoaId && !!state.show,
    })
  );

  useEffect(() => {
    if (state.show) {
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state.show]);

  const listNhanVienTheoPhong = useMemo(() => {
    const { khoaId: currentKhoaId } = state;
    const currentPhongId = phongIdForm;

    if (!listAllNhanVien?.length || !currentKhoaId || !currentPhongId) {
      return [];
    }

    return listAllNhanVien.filter((nhanVien) => {
      const khoa = nhanVien.dsKhoa?.find((k) => k.khoaId === currentKhoaId);
      if (!khoa) return false;

      const isAssignedToPhong = khoa.dsPhongId?.includes(currentPhongId);

      return isAssignedToPhong;
    });
  }, [listAllNhanVien, state, phongIdForm]);

  const hotKeys = [
    {
      keyCode: HOTKEY.ESC,
      onEvent: () => {
        onOk(false)();
      },
    },
    {
      keyCode: HOTKEY.F4,
      onEvent: () => {
        onOk(true)();
      },
    },
  ];

  const onOk = (isOk) => () => {
    if (isOk) {
      form.submit();
    } else {
      setState({ show: false });
    }
  };

  const onHandleSubmit = async () => {
    showLoading();
    try {
      const values = await form.validateFields();
      const { isEdit, khoaId, id } = state;
      const { phongId, nhanVienId, ngayLamViec, thoiGianLamViec } =
        values || {};
      const tuGio = moment(thoiGianLamViec[0]).format("HH:mm:ss");
      const denGio = moment(thoiGianLamViec[1]).format("HH:mm:ss");
      const params = {
        phongId,
        nhanVienId,
        tuThoiGian: moment(ngayLamViec[0]).format(`YYYY/MM/DD ${tuGio}`),
        denThoiGian: moment(ngayLamViec[1]).format(`YYYY/MM/DD ${denGio}`),
        khoaId,
        isEdit,
        ...(isEdit && { id }),
      };
      await crudNvThoiGianLamViec(params);
      searchByParams({});
      setState({ show: false });
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  return (
    <ModalTemplate
      width={520}
      ref={refModal}
      title={t("quanLyNhanLuc.thongTinLichLamViec")}
      hotKeys={hotKeys}
      onCancel={onOk(false)}
      destroyOnClose
      actionLeft={<Button.QuayLai onClick={onOk(false)} />}
      actionRight={
        <Button
          type="primary"
          minWidth={100}
          iconHeight={15}
          onClick={onOk(true)}
        >
          {t("common.luu")}
        </Button>
      }
    >
      <Main>
        <Form
          form={form}
          layout="vertical"
          style={{ width: "100%" }}
          className="form-custom"
          onFinish={onHandleSubmit}
          onValuesChange={(e) => {
            if (e?.hasOwnProperty("phongId")) {
              form.setFieldsValue({ nhanVienId: null });
            }
          }}
        >
          <Form.Item
            label={t("baoCao.tenBacSi")}
            name="nhanVienId"
            style={{ width: "100%" }}
            rules={[
              {
                required: true,
                message: t("danhMuc.vuiLongChonBacSi"),
              },
            ]}
          >
            <Select
              className="select"
              placeholder={t("baoCao.chonBacSi")}
              data={listNhanVienTheoPhong}
              getLabel={selectMaTen}
            />
          </Form.Item>
          <div className="date-group">
            <Form.Item
              label={capitalize(t("common.ngay"))}
              name="ngayLamViec"
              style={{ width: "48%" }}
              rules={[
                {
                  required: true,
                  message: t("common.vuiLongChonNgay"),
                },
              ]}
            >
              <RangePicker
                className="select"
                placeholder={[t("common.tuNgay"), t("common.denNgay")]}
                format={"DD/MM/YYYY"}
              />
            </Form.Item>
            <Form.Item
              label={t("qms.thoiGianLamViec")}
              name="thoiGianLamViec"
              style={{ width: "48%" }}
              rules={[
                {
                  required: true,
                  message: t("qms.vuiLongChonThoiGianLamViec"),
                },
              ]}
            >
              <TimePicker.RangePicker
                className="select"
                placeholder={[t("common.tu"), t("common.den")]}
                format={"HH:mm:ss"}
                order={
                  ngayLamViec &&
                  ngayLamViec[0] &&
                  ngayLamViec[1] &&
                  !moment(ngayLamViec[0]).isSame(ngayLamViec[1], "day")
                    ? false
                    : true
                }
              />
            </Form.Item>
          </div>
          <Form.Item
            label={t("common.phong")}
            name="phongId"
            style={{ width: "100%" }}
            rules={[
              {
                required: true,
                message: t("common.vuiLongNhapPhong"),
              },
            ]}
          >
            <Select
              className="select"
              placeholder={t("common.chonPhong")}
              data={listAllPhong}
            />
          </Form.Item>
        </Form>
      </Main>
    </ModalTemplate>
  );
};

export default forwardRef(ModalThemMoiNhanVien);
