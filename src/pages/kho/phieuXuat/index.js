import React, { useMemo, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import DanhSachHangHoa from "./DanhSachHangHoa";
import { MainPage } from "./styled";
import { useEffect } from "react";
import TrangT<PERSON> from "pages/kho/components/TrangThai";
import { Card, Button, TableWrapper, HeaderSearch } from "components";
import { useParams, useLocation, useHistory } from "react-router-dom";
import FormPhieuXuat from "./FormPhieuXuat";
import ModalNhapLyDo from "pages/kho/components/ModalNhapLyDo";
import { useConfirm, useQueryString, useStore, useThietLap } from "hooks";
import SearchHangHoa from "./SearchHangHoa";
import ThongTinPhieuXuat from "./ChiTiet/ThongTinPhieuXuat";
import { checkRole } from "lib-utils/role-utils";
import {
  LOAI_NHAP_XUAT,
  ROLES,
  THIET_LAP_CHUNG,
  TRANG_THAI_PHIEU_NHAP_XUAT,
} from "constants/index";
import { SVG } from "assets";
import { t } from "i18next";

const validator = {
  fieldValidate: function (type) {
    return (data) => {
      switch (type) {
        case "soLuongYeuCau":
        case "soLuongSoCapYeuCau":
          return (data || []).some((i) => i[type] > i.soLuongSoCapKhaDung);
        default:
          return false;
      }
    };
  },
  validateAll: function (data) {
    return ["soLuongYeuCau", "soLuongSoCapYeuCau"].some((i) =>
      this.fieldValidate(i)(data)
    );
  },
};

const PhieuXuat = ({ ...props }) => {
  const refModalNhapLyDo = useRef(null);
  const refSearchHangHoa = useRef(null);
  const { id } = useParams();
  const location = useLocation();
  const [type] = useQueryString("type", "");
  const [khoId] = useQueryString("khoId", "");
  const history = useHistory();
  const [dataTU_DONG_DUYET_NHAP_KHO] = useThietLap(
    THIET_LAP_CHUNG.TU_DONG_DUYET_NHAP_KHO
  );
  const { thongTinPhieu, dsNhapXuatChiTiet } = useStore(
    "phieuNhapXuat",
    {},
    { fields: "thongTinPhieu, dsNhapXuatChiTiet" }
  );
  const {
    phieuNhapXuat: {
      getById,
      updateData,
      createOrUpdate,
      resetData,
      suaSoLuongDuyet,
      inPhieuNhapXuat,
    },
    hinhThucNhapXuat: { getTongHop: getListHinhThucNhapXuat },
    kho: { getByIdTongHop: searchById },
    danhSachDichVuKho: { kiemTraCoSoDuoi },
  } = useDispatch();

  const [state, _setState] = useState({
    loading: false,
    xuatTheoLo: false,
    dieuChinhCoSo: false,
    dieuChinhCoSoDuoi: false,
  });

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const { showConfirm } = useConfirm();
  const loaiNhapXuat = useMemo(() => {
    if (type) return parseInt(type);
    if (thongTinPhieu.id) return thongTinPhieu.loaiNhapXuat;
    return 0;
  }, [location, type, thongTinPhieu.loaiNhapXuat]);

  const getTitlePage = useMemo(() => {
    let mode = t("common.themMoi");
    if (thongTinPhieu.id) {
      mode = t("common.chinhSua");
    }
    if (loaiNhapXuat == 0) return `${mode} ${t("nhaThuoc.phieuXuat")}`;
    if (loaiNhapXuat == 30) return `${mode} ${t("khoMau.phieuXuatChuyenKho")}`;
    if (loaiNhapXuat == 40) return `${mode} ${t("khoMau.phieuTraNhaCungCap")}`;
    if (loaiNhapXuat == 90) return `${mode} ${t("khoMau.phieuXuatKhac")}`;
    if (loaiNhapXuat == 50) return `${mode} ${t("kho.phieuXuatTraKhoTaiKhoa")}`;
    if (loaiNhapXuat == 45) return `${mode} ${t("kho.phieuXuatDaoHanSuDung")}`;
  }, [loaiNhapXuat, thongTinPhieu]);

  const isEdit = useMemo(() => {
    return !!id;
  }, [id]);

  const times = useMemo(() => {
    const { thoiGianDuyet, thoiGianGuiDuyet, thoiGianTaoPhieu } =
      thongTinPhieu || {};
    return [thoiGianTaoPhieu, thoiGianGuiDuyet, thoiGianDuyet].map(
      (item) => item
    );
  }, [thongTinPhieu]);

  useEffect(() => {
    if (
      thongTinPhieu?.trangThai &&
      ![10, 15, 20].includes(thongTinPhieu?.trangThai) //chỉ 3 trangh thái tạo mới,chờ duyệt mới được vào trang chỉnh sửa, tạo mới
    ) {
      history.push("/kho/xuat-kho/chi-tiet/" + thongTinPhieu.id);
    }
  }, [thongTinPhieu]);

  useEffect(() => {
    getListHinhThucNhapXuat({ dsHinhThucNhapXuat: 20, page: "", size: "" });
    if (id) {
      getById(id).then((res) => {
        setState({
          dieuChinhCoSoDuoi: res.dieuChinhCoSoDuoi,
        });
      });
    } else {
      let dsLoaiDichVu = [];
      async function fetchData() {
        if (khoId) {
          await searchById(khoId).then((s) => {
            dsLoaiDichVu = s?.dsLoaiDichVu;
          });
        }
        updateData({
          thongTinPhieu: {
            ...(thongTinPhieu || {}),
            khoId: khoId ? parseInt(khoId) : "",
            loaiNhapXuat,
            dsLoaiDichVu,
            trangThai: TRANG_THAI_PHIEU_NHAP_XUAT.TAO_MOI,
          },
        });
      }
      fetchData();
    }
    return () => {
      resetData();
    };
  }, []);
  const thanhTien = useMemo(() => {
    return (dsNhapXuatChiTiet || []).reduce((a, b) => {
      return (
        a + b.soLuongSoCapYeuCau * (b.giaNhapSauVat || b.loNhap?.giaNhapSauVat)
      );
    }, 0);
  }, [dsNhapXuatChiTiet]);

  const isDisableLuuVaGuiDuyet = useMemo(() => {
    return validator.validateAll(dsNhapXuatChiTiet);
  }, [dsNhapXuatChiTiet]);

  const contentCanhBao = (data) => {
    return (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          padding: "0 16px",
        }}
      >
        <TableWrapper
          columns={[
            {
              title: (
                <HeaderSearch
                  title={t("common.stt")}
                  className="flex flex-center"
                />
              ),
              width: 50,
              dataIndex: "index",
              key: "index",
              align: "center",
              render: (_, __, index) => index + 1,
            },
            {
              title: (
                <HeaderSearch
                  title={t("kho.tenHangHoa")}
                  className="flex flex-center"
                />
              ),
              width: 200,
              dataIndex: "dichVu",
              key: "dichVu",
              render: (value) => value?.ten,
            },
            {
              title: (
                <HeaderSearch
                  title={t("kho.coSoTren")}
                  className="flex flex-center"
                />
              ),
              width: 120,
              dataIndex: "coSoSoCap",
              key: "coSoSoCap",
            },
            {
              title: (
                <HeaderSearch
                  title={t("common.noiDung")}
                  className="flex flex-center"
                />
              ),
              width: 200,
              dataIndex: "noiDung",
              key: "noiDung",
              render: () => {
                return (
                  <div
                    dangerouslySetInnerHTML={{
                      __html: t("kho.noiDungCanhBaoCoSoDuoi"),
                    }}
                  />
                );
              },
            },
          ]}
          dataSource={data}
        />
      </div>
    );
  };

  const onSave = (guiDuyet) => (e) => {
    setState({ loading: true });

    const onSaveData = () => {
      if (thongTinPhieu.trangThai === 20) {
        suaSoLuongDuyet(id)
          .then(({ id }) => {
            history.push(`/kho/xuat-kho/chi-tiet/${id}`);
            setState({ loading: false });
          })
          .catch(() => {
            setState({ loading: false });
          });
      }

      const object = {
        guiDuyet,
        loaiPhieu: 3,
        thanhTien,
        duyetPhieu:
          dataTU_DONG_DUYET_NHAP_KHO == "true" &&
          loaiNhapXuat == LOAI_NHAP_XUAT.XUAT_CHUYEN_KHO,
        khoaChiDinhId:
          loaiNhapXuat == LOAI_NHAP_XUAT.XUAT_TRA_TAI_KHOA
            ? null
            : thongTinPhieu?.khoaChiDinhId,
        khoaId:
          loaiNhapXuat == LOAI_NHAP_XUAT.XUAT_TRA_TAI_KHOA
            ? thongTinPhieu?.khoaChiDinhId
            : null,
      };
      if (id) object.id = id;
      else {
        object.moi = true;
      }
      createOrUpdate(object)
        .then(({ id }) => {
          history.push(`/kho/xuat-kho/chi-tiet/${id}`);
          inPhieuNhapXuat({ dsId: [id] });
        })
        .catch(() => {})
        .finally(() => {
          setState({ loading: false });
        });
    };

    if (
      state.dieuChinhCoSoDuoi &&
      thongTinPhieu.loaiNhapXuat === LOAI_NHAP_XUAT.XUAT_CHUYEN_KHO
    ) {
      kiemTraCoSoDuoi(
        dsNhapXuatChiTiet.map((item) => ({
          dichVuId: item.dichVuId,
          khoId: thongTinPhieu.khoDoiUngId,
          coSoDuoi: item.soLuongCoSoDuoiSoCap,
        }))
      ).then(async (s) => {
        if (s.data.length) {
          showConfirm(
            {
              title: t("common.canhBao"),
              cancelText: t("common.dong"),
              typeModal: "warning",
              isContentElement: true,
              content: contentCanhBao(s.data),
              showIconContent: false,
              width: 768,
            },
            () => {},
            () => setState({ loading: false })
          );
        } else {
          onSaveData();
        }
      });
    } else {
      onSaveData();
    }
  };

  const onFocusSearchHangHoa = () => {
    refSearchHangHoa.current && refSearchHangHoa.current.show();
  };

  const onCancel = () => {
    history.push(`/kho/xuat-kho/chi-tiet/${thongTinPhieu.id}`);
  };

  return (
    <MainPage
      breadcrumb={[
        { title: t("kho.kho"), link: "/kho" },
        { title: t("kho.xuatKho"), link: "/kho/xuat-kho" },
      ]}
      titleRight={<TrangThai times={times} />}
      title={<>{getTitlePage}</>}
      actionRight={
        !isEdit ? (
          <>
            {checkRole([ROLES["KHO"].GUI_DUYET_PHIEU_XUAT_KHO]) && (
              <Button
                onClick={onSave(true)}
                className="left-btn"
                rightIcon={<SVG.IcGuiVaDuyet />}
                loading={state?.loading}
                disabled={isDisableLuuVaGuiDuyet}
              >
                {t("kho.luuVaGuiDuyetPhieu")}
              </Button>
            )}
            {!checkRole([ROLES["KHO"].GUI_DUYET_PHIEU_XUAT_KHO]) && (
              <Button
                className="right-btn"
                onClick={onSave(false)}
                type={"primary"}
                rightIcon={<SVG.IcSave />}
                loading={state?.loading}
              >
                {t("kho.luuPhieu")}
              </Button>
            )}
          </>
        ) : (
          <>
            <Button
              className="right-btn"
              onClick={onCancel}
              rightIcon={<SVG.IcCloseCircle />}
              minWidth={120}
            >
              {t("common.huy")}
            </Button>
            <Button
              className="right-btn"
              onClick={onSave(false)}
              rightIcon={<SVG.IcSave />}
              minWidth={120}
              type={"primary"}
              loading={state?.loading}
            >
              {t("kho.luuPhieu")}
            </Button>
          </>
        )
      }
    >
      <Card>
        {!thongTinPhieu.trangThai ||
        ([10, 15].includes(thongTinPhieu.trangThai) &&
          thongTinPhieu.loaiNhapXuat != 20) ? (
          <FormPhieuXuat loaiNhapXuat={loaiNhapXuat} />
        ) : (
          <ThongTinPhieuXuat isEdit={true} loaiNhapXuat={loaiNhapXuat} />
        )}
      </Card>
      <Card className="card-content" noPadding={true}>
        <SearchHangHoa
          isEdit={true}
          ref={refSearchHangHoa}
          setParentState={setState}
          parentState={state}
        />
        <DanhSachHangHoa
          parentState={state}
          onFocusSearchHangHoa={onFocusSearchHangHoa}
          {...props}
          isEdit={true}
          validator={validator}
        />
      </Card>
      <ModalNhapLyDo ref={refModalNhapLyDo} />
    </MainPage>
  );
};

export default PhieuXuat;
