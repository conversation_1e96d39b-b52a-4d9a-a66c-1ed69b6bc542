import React, { useMemo, useRef, useEffect, useState } from "react";
import { MainPage } from "./styled";
import { useDispatch } from "react-redux";
import FormPhieuNhap from "./FormPhieuNhap";
import DanhSachHangHoa from "./DanhSachHangHoa";
import { <PERSON><PERSON>, Card, HeaderSearch, TableWrapper } from "components";
import { useHistory, useParams } from "react-router-dom";
import TrangThai from "pages/kho/components/TrangThai";
import moment from "moment";
import { useConfirm, useLoading, useQueryString, useStore } from "hooks";
import SearchHangHoa from "./SearchHangHoa";
import { useTranslation } from "react-i18next";
import { checkRole } from "lib-utils/role-utils";
import { LOAI_DICH_VU, LOAI_NHAP_XUAT, ROLES } from "constants/index";
import { SVG } from "assets";
import { isArray } from "utils/index";

const PhieuNhapDuTru = ({ ...props }) => {
  const { showConfirm } = useConfirm();
  const { showLoading, hideLoading } = useLoading();
  const [khoId] = useQueryString("khoId", "");
  const refDanhSachHangHoa = useRef(null);
  const { t } = useTranslation();
  const thongTinPhieu = useStore("phieuNhapXuat.thongTinPhieu");
  const {
    phieuNhapXuat: { updateData, createOrUpdate, getById, resetData },
    danhSachDichVuKho: { kiemTraCoSoDuoi },
    kho: { getByIdTongHop: searchById },
  } = useDispatch();
  const { id } = useParams();

  const [state, _setState] = useState({
    dieuChinhCoSoDuoi: false,
    nhapTheoLo: false,
    nhapHsdXaNhat: false,
    dsPhieuLinhId: [],
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const refSearchHangHoa = useRef(null);
  const history = useHistory();
  const times = useMemo(() => {
    const { thoiGianDuyet, thoiGianGuiDuyet, thoiGianTaoPhieu } =
      thongTinPhieu || {};
    return [thoiGianTaoPhieu, thoiGianGuiDuyet, thoiGianDuyet].map(
      (item) => item
    );
  }, [thongTinPhieu]);

  useEffect(() => {
    if (id) {
      getById(id);
    } else {
      let dsLoaiDichVu = [];
      async function fetchData() {
        await searchById(khoId).then((s) => {
          dsLoaiDichVu = s?.dsLoaiDichVu;
        });
        updateData({
          thongTinPhieu: {
            khoId: khoId ? parseInt(khoId) : "",
            ngayHoaDon: moment(new Date(), "YYYY-MM-DD"),
            loaiNhapXuat: 20,
            dsLoaiDichVu: dsLoaiDichVu,
          },
        });
      }
      fetchData();
    }
    return () => {
      resetData();
    };
  }, []);

  useEffect(() => {
    if (thongTinPhieu) {
      setState({
        dieuChinhCoSoDuoi: !!thongTinPhieu.dieuChinhCoSoDuoi,
      });
    }
  }, [thongTinPhieu]);

  const isEdit = useMemo(() => {
    return !!id;
  }, [id]);

  const showGuiDuyet = useMemo(() => {
    const { dsLoaiDichVu, loaiNhapXuat } = thongTinPhieu || {};
    if (isArray(dsLoaiDichVu, true) && loaiNhapXuat === LOAI_NHAP_XUAT.DU_TRU) {
      if (dsLoaiDichVu.includes(LOAI_DICH_VU.THUOC)) {
        return checkRole([ROLES["KHO"].GUI_DUYET_PHIEU_NHAP_KHO_THUOC]);
      } else if (
        dsLoaiDichVu.includes(LOAI_DICH_VU.VAT_TU) ||
        dsLoaiDichVu.includes(LOAI_DICH_VU.HOA_CHAT)
      ) {
        return checkRole([ROLES["KHO"].GUI_DUYET_PHIEU_NHAP_KHO_VT_HOA_CHAT]);
      }
      return checkRole([ROLES["KHO"].GUI_DUYET_PHIEU_XUAT_KHO]);
    }
    return checkRole([ROLES["KHO"].GUI_DUYET_PHIEU_XUAT_KHO]);
  }, [thongTinPhieu]);

  const onCancel = () => {
    history.push(`/kho/phieu-nhap-du-tru/chi-tiet/${thongTinPhieu.id}`);
  };

  const contentCanhBao = (data) => {
    return (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          padding: "0 16px",
        }}
      >
        <TableWrapper
          columns={[
            {
              title: (
                <HeaderSearch
                  title={t("common.stt")}
                  className="flex flex-center"
                />
              ),
              width: 50,
              dataIndex: "index",
              key: "index",
              align: "center",
              render: (_, __, index) => index + 1,
            },
            {
              title: (
                <HeaderSearch
                  title={t("kho.tenHangHoa")}
                  className="flex flex-center"
                />
              ),
              width: 200,
              dataIndex: "dichVu",
              key: "dichVu",
              render: (value) => value?.ten,
            },
            {
              title: (
                <HeaderSearch
                  title={t("kho.coSoTren")}
                  className="flex flex-center"
                />
              ),
              width: 120,
              dataIndex: "coSoSoCap",
              key: "coSoSoCap",
            },
            {
              title: (
                <HeaderSearch
                  title={t("common.noiDung")}
                  className="flex flex-center"
                />
              ),
              width: 200,
              dataIndex: "noiDung",
              key: "noiDung",
              render: () => (
                <div
                  dangerouslySetInnerHTML={{
                    __html: t("kho.noiDungCanhBaoCoSoDuoi"),
                  }}
                />
              ),
            },
          ]}
          dataSource={data}
        />
      </div>
    );
  };

  const onSave = (guiDuyet) => (e) => {
    let dsNhapXuatChiTiet =
      refDanhSachHangHoa.current && refDanhSachHangHoa.current.getData();
    let validateSave = (dsNhapXuatChiTiet || []).find((x) => x.validateSoLuong);
    if (validateSave) {
      return null;
    } else {
      showLoading();

      setState({ isLoadingBtn: true });

      const onSaveData = () => {
        createOrUpdate({ id, guiDuyet, loaiPhieu: 1 })
          .then(({ id }) => {
            hideLoading();
            history.push(`/kho/phieu-nhap-du-tru/chi-tiet/${id}`);
            setState({ isLoadingBtn: false });
          })
          .catch(() => {
            hideLoading();
          });
      };

      if (state.dieuChinhCoSoDuoi) {
        kiemTraCoSoDuoi(
          dsNhapXuatChiTiet.map((item) => ({
            dichVuId: item.dichVuId,
            khoId: thongTinPhieu.khoId, // dùng kho nhập(khoId)
            coSoDuoi: item.soLuongCoSoDuoiSoCap,
          }))
        )
          .then(async (s) => {
            if (s.data.length) {
              showConfirm(
                {
                  title: t("common.canhBao"),
                  cancelText: t("common.dong"),
                  typeModal: "warning",
                  isContentElement: true,
                  content: contentCanhBao(s.data),
                  showIconContent: false,
                  width: 768,
                },
                () => {},
                () => {
                  setState({ isLoadingBtn: false });
                }
              );
              hideLoading();
            } else {
              onSaveData();
            }
          })
          .catch((error) => {
            console.error(error?.message || error);
            hideLoading();
          });
      } else {
        onSaveData();
      }

      setTimeout(() => setState({ isLoadingBtn: false }), 1000);
    }
  };

  const onFocusSearchHangHoa = () => {
    refSearchHangHoa.current && refSearchHangHoa.current.show();
  };

  return (
    <MainPage
      breadcrumb={[
        { title: t("kho.kho"), link: "/kho" },
        { title: t("kho.nhapKho"), link: "/kho/nhap-kho" },
      ]}
      titleRight={<TrangThai times={times} />}
      title={t("kho.phieuNhapDuTru")}
      actionRight={
        !isEdit ? (
          <>
            {showGuiDuyet && (
              <Button
                loading={state?.isLoadingBtn}
                onClick={onSave(true)}
                className="left-btn"
                rightIcon={<SVG.IcGuiVaDuyet />}
              >
                {t("kho.luuVaGuiDuyetPhieu")}
              </Button>
            )}
            {!showGuiDuyet && (
              <Button
                loading={state?.isLoadingBtn}
                className="right-btn"
                onClick={onSave(false)}
                type={"primary"}
                rightIcon={<SVG.IcSave />}
              >
                {t("kho.luuPhieu")}
              </Button>
            )}
          </>
        ) : (
          <>
            <Button
              className="right-btn"
              onClick={onCancel}
              rightIcon={<SVG.IcCloseCircle />}
              minWidth={120}
            >
              {t("kho.huy")}
            </Button>
            <Button
              loading={state?.isLoadingBtn}
              className="right-btn"
              onClick={onSave(false)}
              rightIcon={<SVG.IcSave />}
              minWidth={120}
              type={"primary"}
            >
              {t("kho.luuPhieu")}
            </Button>
          </>
        )
      }
    >
      <Card>
        <FormPhieuNhap {...props} />
      </Card>
      <Card noPadding={true} className="card-content">
        <div
          style={{ height: "100%", overflow: "hidden" }}
          className="flex flex-col"
        >
          <SearchHangHoa
            setParentState={setState}
            parentState={state}
            isEdit={true}
            ref={refSearchHangHoa}
          />
          <DanhSachHangHoa
            onFocusSearchHangHoa={onFocusSearchHangHoa}
            ref={refDanhSachHangHoa}
            parentState={state}
            {...props}
            isEdit={true}
          />
        </div>
      </Card>
    </MainPage>
  );
};

export default PhieuNhapDuTru;
