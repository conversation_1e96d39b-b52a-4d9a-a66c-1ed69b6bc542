import React, { useMemo, useRef } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>er<PERSON><PERSON>ch, TableWrapper } from "components";
import moment from "moment";
import TableEmpty from "pages/kho/components/TableEmpty";
import { useSelector, useDispatch } from "react-redux";
import { formatNumber, roundToDigits } from "utils/index";
import PopoverHangHoa from "../../PopoverHangHoa";
import { Main } from "./styled";
import { orderBy } from "lodash";
import { useTranslation } from "react-i18next";
import { Switch } from "antd";
import { useLoading, useThietLap } from "hooks";
import { THIET_LAP_CHUNG, ROLES } from "constants/index";
import { SVG } from "assets";
import { checkRole } from "lib-utils/role-utils";
import { getTenHangHoa, tinhThanhTienVat } from "utils/kho-utils";

const DanhSachHangHoa = ({
  dataSortColumn,
  onClickSort,
  detachLine = true,
  detail = {},
  ...props
}) => {
  const refSettings = useRef(null);

  const { t } = useTranslation();
  const { dsNhapXuatChiTiet, thongTinPhieu } = useSelector(
    (state) => state.phieuNhapXuat
  );
  const [dataNHAP_KHO_HIEN_THI_VAT_BAN] = useThietLap(
    THIET_LAP_CHUNG.NHAP_KHO_HIEN_THI_VAT_BAN
  );
  const [dataNHAP_KICH_CO_VAT_TU] = useThietLap(
    THIET_LAP_CHUNG.NHAP_KICH_CO_VAT_TU
  );
  const { showLoading, hideLoading } = useLoading();

  const {
    phieuNhapXuat: { inTemThuocNCC },
  } = useDispatch();

  const isShowVatBan = useMemo(() => {
    return (dataNHAP_KHO_HIEN_THI_VAT_BAN || "")
      .split(",")
      .includes(thongTinPhieu.kho?.ma);
  }, [dataNHAP_KHO_HIEN_THI_VAT_BAN, thongTinPhieu]);

  const isNhapNcc = window.location.pathname.includes(
    "/kho/phieu-nhap-nha-cung-cap/chi-tiet/"
  );

  const dataSource = useMemo(() => {
    let data = [];

    const _dsNhapXuatChiTiet = orderBy(dsNhapXuatChiTiet || [], "id", "asc");
    _dsNhapXuatChiTiet.map((item, index) => {
      let thanhTienTruocVat = roundToDigits(
        item?.loNhap?.giaNhapTruocVat * (item.soLuongSoCap || 0),
        3
      );

      data.push({
        ...item,
        ...(isNhapNcc && {
          thanhTienTruocVat,
          thanhTienSuaDoiTruocVat: item.thanhTienSuaDoiTruocVat,
          thanhTienVat: tinhThanhTienVat(
            item.thanhTienSuaDoi,
            item.thanhTienSuaDoiTruocVat
          ),
        }),
        index: index + 1,
      });
      (item.dsNhapXuatChiTiet || []).map((item2, index2) => {
        data.push({
          ...item2,
          index: index + 1 + "." + (index2 + 1),
          ma: item2?.dichVu?.ma || item2?.ma,
          ten: item2?.dichVu?.ten || item2?.ten,
        });
      });
    });

    return (data || []).reduce((a, item, index) => {
      return [
        ...a,
        {
          ...item,
          rowId: index,
        },
        {
          ...item,
          rowId: index + "_",
        },
      ];
    }, []);
  }, [dsNhapXuatChiTiet, isNhapNcc]);

  const onSettings = () => {
    refSettings && refSettings.current.settings();
  };

  const onPrintTemThuoc = (item) => (e) => {
    e.preventDefault();
    e.stopPropagation();
    showLoading();
    inTemThuocNCC({ dsId: item?.id }).finally(hideLoading);
  };

  const renderInfo = (data) => {
    return (
      <div className="product-info">
        <div className="table_line line_1">
          {thongTinPhieu.loaiChietKhau === 20 ? (
            <div className="table_line-item flex-15">
              <Switch
                checkedChildren="đ"
                unCheckedChildren="%"
                checked={data?.loNhap?.tienChietKhau !== null}
              />
              <div style={{ marginLeft: 5 }} className="table_line-item-label">
                {t("kho.chietKhau")}:{" "}
              </div>
              <div>
                {data?.loNhap?.tienChietKhau
                  ? formatNumber(data?.loNhap?.tienChietKhau || 0, 3)
                  : data?.loNhap?.phanTramChietKhau}
              </div>
            </div>
          ) : (
            <div className="table_line-item flex-1">
              <div className="table_line-item-label">
                {t("kho.quyetDinhThau.maHieu")}:{" "}
              </div>
              <div>{data?.loNhap?.maKyHieu}</div>
            </div>
          )}
          <div className="table_line-item flex-1">
            <div className="table_line-item-label">
              {t("kho.quyetDinhThau.soVisa")}:{" "}
            </div>
            <div>
              {data.chiTietThau?.soVisa ||
                data.loNhap?.quyetDinhThauChiTiet?.soVisa ||
                data.loNhap?.soVisa ||
                data.dichVu?.soVisa}
            </div>
          </div>
          <div className="table_line-item flex-2">
            <div className="table_line-item-label">
              {t("kho.quyetDinhThau.tenTrungThau")}:{" "}
            </div>
            <Tooltip title={data?.loNhap?.quyetDinhThauChiTiet?.tenTrungThau}>
              <div>{data?.loNhap?.quyetDinhThauChiTiet?.tenTrungThau}</div>
            </Tooltip>
          </div>
          <div className="table_line-item flex-1">
            <div className="table_line-item-label">{t("kho.soLo")}: </div>
            <div>{data?.loNhap?.soLo}</div>
          </div>
          <div className="table_line-item flex-1">
            <div className="table_line-item-label">{t("kho.hsd")}: </div>
            <div>
              {data?.loNhap?.ngayHanSuDung &&
                moment(data.loNhap.ngayHanSuDung).format("DD/MM/YYYY")}
            </div>
          </div>
          <div className="table_line-item flex-1">
            <div className="table_line-item-label">
              {t("danhMuc.thangSoBanLe")}:{" "}
            </div>
            <div>{data?.loNhap?.thangSoBanLe || 0}</div>
          </div>
          {isShowVatBan && (
            <div className="table_line-item flex-1">
              <div className="table_line-item-label">{t("kho.vatBan")}: </div>
              <div>{data?.loNhap?.vatBan || 0}</div>
            </div>
          )}
        </div>
        <div className="table_line">
          <div className="table_line-item flex-1">
            <div className="table_line-item-label">
              {t("danhMuc.donGiaBh")}:{" "}
            </div>
            <div>{formatNumber(data?.loNhap?.giaBaoHiem || 0, 3)}</div>
          </div>
          {checkRole([ROLES["KHO"].HIEN_THI_TY_LE_THANH_TOAN_DANH_MUC]) && (
            <Popover
              placement="bottomRight"
              content={
                <>
                  <div>
                    {t("kho.quyetDinhThau.tiLeThanhToanBHDanhMuc")}:{" "}
                    {data?.tyLeBhTt}
                  </div>
                  <div>
                    {t("kho.quyetDinhThau.tiLeThanhToanDichVuDanhMuc")}:{" "}
                    {data?.tyLeTtDv}
                  </div>
                </>
              }
            >
              <div style={{ margin: "0 5px" }}>
                <SVG.IcInfo style={{ width: 20, height: 20 }} />
              </div>
            </Popover>
          )}
          <div className="table_line-item flex-2">
            <div className="table_line-item-label">
              {t("danhMuc.donGiaKhongBh")}:{" "}
            </div>
            <div>{formatNumber(data?.loNhap?.giaKhongBaoHiem || 0, 3)}</div>
          </div>
          <div className="table_line-item flex-1">
            <div className="table_line-item-label">{t("danhMuc.phuThu")}: </div>
            <div>{formatNumber(data?.loNhap?.giaPhuThu || 0, 3)}</div>
          </div>
          <div className="table_line-item flex-1">
            <div className="table_line-item-label">{t("kho.tyLeBhTt")}: </div>
            <div>
              {data.loNhap?.quyetDinhThauChiTietId
                ? data.loNhap?.quyetDinhThauChiTiet?.tyLeBhTt
                : data.dichVu?.tyLeBhTt}
            </div>
          </div>
          <div className="table_line-item flex-1">
            <div className="table_line-item-label">{t("kho.tyLeTtDv")}: </div>
            <div>
              {data.loNhap?.quyetDinhThauChiTietId
                ? data.loNhap?.quyetDinhThauChiTiet?.tyLeTtDv
                : data.dichVu?.tyLeTtDv}
            </div>
          </div>
          <div className="table_line-item flex-1">
            <div className="table_line-item-label">
              {t("common.nhaSanXuat")}:{" "}
            </div>
            <div className="nha-san-xuat">{data.loNhap?.nhaSanXuat?.ten}</div>
          </div>
          <div className="table_line-item flex-1">
            <div className="table_line-item-label">{t("danhMuc.xuatXu")}: </div>
            <div>{data.loNhap?.xuatXu?.ten}</div>
          </div>
        </div>
      </div>
    );
  };

  const columns = [
    {
      title: <HeaderSearch title={t("common.stt")} minHeight={0} />,
      key: "stt",
      width: 50,
      ellipsis: {
        showTitle: false,
      },
      render: (_, data, index) =>
        index % 2 === 1 ? (
          {
            children: renderInfo(data),
            props: {
              colSpan: isNhapNcc ? 12 : 11,
            },
          }
        ) : (
          <div>{data?.index}</div>
        ),
    },
    {
      title: (
        <HeaderSearch
          minHeight={0}
          title={t("kho.maHangHoa")}
          sort_key="dichVu.dichVu.ma"
          onClickSort={onClickSort}
          dataSort={dataSortColumn?.["dichVu.dichVu.ma"] || 0}
        />
      ),
      key: "ma",
      width: 120,
      show: true,
      i18Name: "kho.maHangHoa",
      render: (_, data, index) =>
        index % 2 === 1 ? (
          {
            children: index + 1,
            props: {
              colSpan: 0,
            },
          }
        ) : (
          // <PopoverHangHoa data={data}>
          <div className="item-ma fit-content">
            {data?.vatTuKichCo && dataNHAP_KICH_CO_VAT_TU == "1"
              ? data?.loNhap?.kichCoVt?.ma
              : data?.dichVu?.ma}
          </div>
          // </PopoverHangHoa>
        ),
    },
    {
      title: (
        <HeaderSearch
          minHeight={0}
          title={t("kho.tenHangHoa")}
          sort_key="dichVu.dichVu.ten"
          onClickSort={onClickSort}
          dataSort={dataSortColumn?.["dichVu.dichVu.ten"] || 0}
        />
      ),
      key: "ten",
      width: 450,
      show: true,
      i18Name: "kho.tenHangHoa",
      render: (_, data, index) =>
        index % 2 === 1 ? (
          {
            children: index + 1,
            props: {
              colSpan: 0,
            },
          }
        ) : (
          <div className="fit-content">
            <PopoverHangHoa data={data}>
              <div className="item-ten">
                {(() => {
                  const ten = getTenHangHoa(data, dataNHAP_KICH_CO_VAT_TU);
                  return data?.tenHoatChat
                    ? `${ten} - ${data.tenHoatChat}`
                    : ten;
                })()}
              </div>
            </PopoverHangHoa>
            <div className="font-normal">{data?.loNhap?.ghiChu}</div>
          </div>
        ),
    },
    {
      title: (
        <HeaderSearch
          minHeight={0}
          title={t("kho.sl")}
          sort_key="soLuongSoCap"
          onClickSort={onClickSort}
          dataSort={dataSortColumn?.soLuongSoCap || 0}
        />
      ),
      dataIndex: "soLuongSoCap",
      key: "soLuongSoCap",
      width: 100,
      align: "center",
      show: true,
      i18Name: "kho.sl",
      render: (item, data, index) =>
        index % 2 === 1 ? (
          {
            children: index + 1,
            props: {
              colSpan: 0,
            },
          }
        ) : (
          <div className="bold">
            {formatNumber(item, 3) +
              " " +
              (data.dvtSoCap?.ten || data?.tenDvtSoCap || "")}
          </div>
        ),
    },
    ...(thongTinPhieu.loaiChietKhau == 20
      ? [
          {
            title: <HeaderSearch title={t("kho.giaTruocVATChuaChietKhau")} />,
            width: 100,
            dataIndex: "giaNhapTruocVat",
            key: "giaNhapTruocVat",
            show: thongTinPhieu.loaiChietKhau == 20,
            i18Name: "kho.giaTruocVATChuaChietKhau",
            render: (item, data, index) =>
              index % 2 === 1 ? (
                {
                  children: index + 1,
                  props: {
                    colSpan: 0,
                  },
                }
              ) : (
                <div className="bold">
                  {formatNumber(
                    data?.loNhap?.tienChietKhau !== null
                      ? parseFloat(data?.loNhap?.giaNhapTruocVat || 0) +
                          parseFloat(data?.loNhap?.tienChietKhau || 0) /
                            parseFloat(data?.soLuongSoCap)
                      : parseFloat(data?.loNhap?.giaNhapTruocVat || 0) /
                          (1 -
                            parseFloat(data?.loNhap?.phanTramChietKhau || 0) /
                              100),
                    3
                  )}
                </div>
              ),
          },
        ]
      : []),
    {
      title: (
        <HeaderSearch
          title={t("kho.giaTruocVAT")}
          sort_key="loNhap.giaNhapTruocVat"
          onClickSort={onClickSort}
          dataSort={dataSortColumn?.["loNhap.giaNhapTruocVat"] || 0}
        />
      ),
      width: 140,
      dataIndex: "giaNhapTruocVat",
      key: "giaNhapTruocVat",
      show: true,
      i18Name: "kho.giaTruocVAT",
      render: (item, data, index) =>
        index % 2 === 1 ? (
          {
            children: index + 1,
            props: {
              colSpan: 0,
            },
          }
        ) : (
          <div className="bold">
            {data?.loNhap?.giaNhapTruocVat?.formatPrice()}
          </div>
        ),
    },
    {
      title: (
        <HeaderSearch
          title="VAT"
          sort_key="loNhap.vat"
          onClickSort={onClickSort}
          dataSort={dataSortColumn?.["loNhap.vat"] || 0}
        />
      ),
      width: 100,
      dataIndex: "vat",
      key: "vat",
      show: true,
      i18Name: "VAT",
      render: (item, data, index) =>
        index % 2 === 1 ? (
          {
            children: index + 1,
            props: {
              colSpan: 0,
            },
          }
        ) : (
          <div className="bold">{data?.loNhap?.vat || 0}</div>
        ),
    },
    {
      title: (
        <HeaderSearch
          minHeight={0}
          title={t("khoMau.giaSauVAT")}
          sort_key="loNhap.giaNhapSauVat"
          onClickSort={onClickSort}
          dataSort={dataSortColumn?.["loNhap.giaNhapSauVat"] || 0}
        />
      ),
      dataIndex: "giaNhapSauVat",
      key: "giaNhapSauVat",
      width: 140,
      align: "center",
      show: true,
      i18Name: "khoMau.giaSauVAT",
      render: (_, data, index) =>
        index % 2 === 1 ? (
          {
            children: index + 1,
            props: {
              colSpan: 0,
            },
          }
        ) : (
          <div className="bold">
            {data?.loNhap?.giaNhapSauVat?.formatPrice()}
            {!thongTinPhieu?.nhapKhongTheoThau &&
              isNhapNcc &&
              data?.loNhap?.quyetDinhThauChiTiet?.giaNhatSauVat &&
              data?.loNhap?.quyetDinhThauChiTiet?.giaNhatSauVat !==
                data?.loNhap?.giaNhapSauVat && (
                <div className="error label-error">
                  {t("kho.giaThauGia", {
                    gia: data?.loNhap?.quyetDinhThauChiTiet?.giaNhatSauVat?.formatPrice(),
                  })}
                </div>
              )}
          </div>
        ),
    },
    {
      title: (
        <HeaderSearch minHeight={0} title={t("khoMau.thanhTienTruocVat")} />
      ),
      dataIndex: "thanhTienTruocVat",
      key: "thanhTienTruocVat",
      width: 140,
      align: "center",
      show: true,
      i18Name: "khoMau.thanhTienTruocVat",
      hidden: !isNhapNcc,
      render: (_, data, index) =>
        index % 2 === 1 ? (
          {
            children: index + 1,
            props: {
              colSpan: 0,
            },
          }
        ) : (
          <Popover
            placement="bottom"
            overlayInnerStyle={{ borderRadius: "5px", borderColor: "" }}
            content={
              <div>
                <label>{t("kho.thanhTienTruocVatSuaDoi")}</label>
                <p>{data?.thanhTienSuaDoiTruocVat?.formatPrice()}</p>
                <hr
                  style={{
                    borderTop: "1px solid #c5cad3",
                    marginLeft: "-12px",
                    marginRight: "-12px",
                  }}
                />
                <label>{t("kho.thanhTienVat")}</label>
                <p>{data?.thanhTienVat?.formatPrice()}</p>
              </div>
            }
          >
            <div className="pointer bold">
              {data?.thanhTienTruocVat?.formatPrice()}
            </div>
          </Popover>
        ),
    },
    {
      title: (
        <HeaderSearch
          minHeight={0}
          title={t("khoMau.thanhTien")}
          sort_key="thanhTien"
          onClickSort={onClickSort}
          dataSort={dataSortColumn?.thanhTien || 0}
        />
      ),
      dataIndex: "thanhTien",
      key: "thanhTien",
      width: 140,
      align: "center",
      show: true,
      i18Name: "khoMau.thanhTien",
      render: (_, data, index) =>
        index % 2 === 1 ? (
          {
            children: index + 1,
            props: {
              colSpan: 0,
            },
          }
        ) : (
          <Popover
            placement="bottom"
            overlayInnerStyle={{ borderRadius: "5px", borderColor: "" }}
            content={
              <div>
                <label>{t("khoMau.thanhTien")}</label>
                <p>{data?.thanhTien?.formatPrice()}</p>
              </div>
            }
          >
            <div className="pointer bold">{data?.thanhTien?.formatPrice()}</div>
          </Popover>
        ),
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.slDuocPhepMua")}
          sort_key="soLuongDuocPhepMua"
          onClickSort={onClickSort}
          dataSort={dataSortColumn?.["soLuongDuocPhepMua"] || 0}
        />
      ),
      width: 140,
      dataIndex: "soLuongDuocPhepMua",
      key: "soLuongDuocPhepMua",
      show: true,
      i18Name: "kho.quyetDinhThau.slDuocPhepMua",
      render: (_, record, index) => {
        return index % 2 === 1 ? (
          {
            children: index + 1,
            props: {
              colSpan: 0,
            },
          }
        ) : (
          <div className="bold">
            {record?.loNhap?.quyetDinhThauChiTiet?.soLuongDuocPhepMua}
          </div>
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.soLuongConLai")}
          sort_key="soLuongConLai"
          onClickSort={onClickSort}
          dataSort={dataSortColumn?.["soLuongConLai"] || 0}
        />
      ),
      dataIndex: "soLuongConLai",
      key: "soLuongConLai",
      width: 140,
      align: "right",
      render: (item, data, index) => {
        return index % 2 === 1 ? (
          {
            children: index + 1,
            props: {
              colSpan: 0,
            },
          }
        ) : (
          <div className="bold">
            {data?.loNhap?.quyetDinhThauChiTiet?.soLuongConLai}
          </div>
        );
      },
    },
    {
      title: <HeaderSearch title={t("kho.quyetDinhThau.giaSauVatThau")} />,
      dataIndex: "giaSauVatThau",
      key: "giaSauVatThau",
      width: 140,
      align: "right",
      render: (_, data, index) => {
        return index % 2 === 1 ? (
          {
            children: index + 1,
            props: {
              colSpan: 0,
            },
          }
        ) : (
          <div className="bold">
            {data?.loNhap?.quyetDinhThauChiTiet?.giaNhatSauVat?.formatPrice()}
          </div>
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={
            <>
              {t("common.tienIch")}{" "}
              <SVG.IcSetting
                onClick={onSettings}
                style={{ cursor: "pointer" }}
              />
            </>
          }
        />
      ),
      width: 80,
      align: "center",
      fixed: "right",
      render: (item, _, index) => {
        return (
          index % 2 === 0 && (
            <Tooltip title={t("kho.inTemThuocNCC")}>
              <SVG.IcPrint
                onClick={onPrintTemThuoc(item)}
                className="ic-action"
              />
            </Tooltip>
          )
        );
      },
    },
  ];

  return (
    <Main>
      <TableWrapper
        className="danh-sach-hang-hoa"
        locale={{
          emptyText: <TableEmpty />,
        }}
        showSorterTooltip={false}
        rowClassName={(record, index) =>
          index % 2 === 1 ? "none-border-top" : "border-top"
        }
        columns={columns}
        dataSource={dataSource}
        pagination={false}
        rowKey={(record) => record.rowId}
        ref={refSettings}
        tableName="table_Kho_ChiTietPhieuNhap_DsHangHoa"
        alwayGetFromCache
      />
    </Main>
  );
};

export default DanhSachHangHoa;
