import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from "react";
import { Col, Form, Input, message, Row, Select } from "antd";
import {
  Checkbox,
  Button,
  Select as CustomSelect,
  DateTimePicker,
} from "components";
import { useDispatch, useSelector } from "react-redux";
import moment from "moment";
import { Main } from "./styled";
import { containText, isArray, openInNewTab } from "utils";
import Header1 from "pages/kho/components/Header1";
import { useHistory } from "react-router-dom";
import { checkRole } from "lib-utils/role-utils";
import {
  LOAI_KHO,
  ROLES,
  THIET_LAP_CHUNG,
  LOAI_NHAP_XUAT,
} from "constants/index";
import { useStore, useThietLap, useListAll } from "hooks";
import { useTranslation } from "react-i18next";
import { isEmpty } from "lodash";

const { Option } = Select;
const { Item } = Form;

const FormPhieuNhap = (
  {
    actionComponent,
    form,
    isEdit,
    setIsFormChange,
    isSuaSoLuong = false,
    ...props
  },
  ref
) => {
  const { t } = useTranslation();
  const history = useHistory();
  const { thongTinPhieu, dsNhapXuatChiTiet, nhapKhongTheoThau } = useSelector(
    (state) => state.phieuNhapXuat
  );

  const [dataHINH_THUC_NHAP_XUAT_KHO_THUOC] = useThietLap(
    THIET_LAP_CHUNG.HINH_THUC_NHAP_XUAT_KHO_THUOC
  );
  const [dataNGUON_NHAP_KHO_THUOC] = useThietLap(
    THIET_LAP_CHUNG.NGUON_NHAP_KHO_THUOC
  );
  const [BAT_BUOC_NHAP_HINH_THUC_NHAP] = useThietLap(
    THIET_LAP_CHUNG.BAT_BUOC_NHAP_HINH_THUC_NHAP
  );
  const [
    CHI_HIEN_THI_THAU_CON_HIEU_LUC_KHI_NHAP_KHO,
    finishCHI_HIEN_THI_THAU_CON_HIEU_LUC_KHI_NHAP_KHO,
  ] = useThietLap(THIET_LAP_CHUNG.CHI_HIEN_THI_THAU_CON_HIEU_LUC_KHI_NHAP_KHO);

  const dataCHI_HIEN_THI_THAU_CON_HIEU_LUC_KHI_NHAP_KHO =
    CHI_HIEN_THI_THAU_CON_HIEU_LUC_KHI_NHAP_KHO.toLowerCase() === "true";
  const [
    dataKHONG_MAC_DINH_TICH_NHAP_KHONG_THAU_NT,
    finishMAC_DINH_TICH_NHAP_KHONG_THAU_NT,
  ] = useThietLap(THIET_LAP_CHUNG.KHONG_MAC_DINH_TICH_NHAP_KHONG_THAU_NT);

  const listHinhThucTongHop = useStore("hinhThucNhapXuat.listTongHop", []);
  const listQuyetDinhThauTongHop = useStore("quyetDinhThau.listDataTongHop");
  const listNguonNhapKho = useStore("nguonNhapKho.listDataTongHop", []);
  const listDataTongHop = useStore("doiTac.listDataTongHop");
  const khoHienTai = useStore("kho.currentItem", {});
  const listKhoUser = useStore("kho.listKhoUser");
  const [listAllBenhVien] = useListAll("benhVien", {}, true);
  const listPhieuXuat = useStore("phieuXuat.listPhieuXuat", []);

  const {
    quyetDinhThau: { searchTongHop },
    nhapKho: { kiemTraSoHoaDon },
    nguonNhapKho: { onSearchTongHop: searchNguonNhapKho },
    hinhThucNhapXuat: { getTongHop },
    kho: { getByIdTongHop, getTheoTaiKhoan },
    phieuNhapXuat: { updateData },
    doiTac: { getListTongHop },
    phieuXuat: { getListPhieuXuat },
  } = useDispatch();

  const formRef = useRef();
  const nhapKhongTheoThauForm = Form.useWatch("nhapKhongTheoThau", form);
  const ngayHopDongForm = Form.useWatch("ngayHopDong", form);
  const ngayHetHanHopDongForm = Form.useWatch("ngayHetHanHopDong", form);
  const soHopDongForm = Form.useWatch("soHopDong", form);

  const [state, _setState] = useState({
    ngayHoaDon: new Date(),
    loaiNhapXuat: 10,
    isKhoKyGui: false,
  });
  const setState = (data = {}) => {
    _setState((_state) => ({
      ..._state,
      ...data,
    }));
  };

  const isBatBuocHinhThucNhap = useMemo(() => {
    return (
      BAT_BUOC_NHAP_HINH_THUC_NHAP &&
      BAT_BUOC_NHAP_HINH_THUC_NHAP.toLowerCase() == "true"
    );
  }, [BAT_BUOC_NHAP_HINH_THUC_NHAP]);

  const hinhThucNhapXuatKhoThuocId = useMemo(
    () =>
      listHinhThucTongHop.find(
        (item) => item.ma === dataHINH_THUC_NHAP_XUAT_KHO_THUOC
      )?.id,
    [listHinhThucTongHop, dataHINH_THUC_NHAP_XUAT_KHO_THUOC]
  );

  const nguonNhapKhoThuocId = useMemo(
    () =>
      listNguonNhapKho.find((item) => item.ma === dataNGUON_NHAP_KHO_THUOC)?.id,
    [listNguonNhapKho, dataNGUON_NHAP_KHO_THUOC]
  );

  const listAllBV = useMemo(() => {
    let result = [];
    if (isArray(listAllBenhVien, true)) {
      result = listAllBenhVien.map((item) => ({
        ...item,
        ten: `${item.ma} - ${item.ten}`,
      }));
    }
    return result;
  }, [listAllBenhVien]);

  const listQuyetDinhThauTongHopMemo = useMemo(() => {
    return listQuyetDinhThauTongHop.map((item) => ({
      id: item.id,
      ten: `${item.quyetDinhThau} ${item.soThau ? `- ${item.soThau}` : ""}`,
    }));
  }, [listQuyetDinhThauTongHop]);

  useImperativeHandle(ref, () => ({
    setData: (data) => {
      //TODO: set value form
      const thongTinForm = [
        "khoId",
        "soHoaDon",
        "kyHieuHoaDon",
        "ngayHoaDon",
        "soHopDong",
        "ngayHopDong",
        "ngayHetHanHopDong",
        "quyetDinhThauId",
        "nguonNhapKhoId",
        "hinhThucNhapXuatId",
      ].reduce(
        (a, item) => ({
          ...a,
          [item]: data[item],
        }),
        {}
      );
      if (khoHienTai.nhaThuoc) {
        thongTinForm.nguonNhapKhoId = nguonNhapKhoThuocId;
        thongTinForm.hinhThucNhapXuatId = hinhThucNhapXuatKhoThuocId;
      }

      form.setFieldsValue(thongTinForm);
      setState({ id: data?.id });
    },
  }));

  useEffect(() => {
    const thongTinForm = [
      "khoId",
      "soHoaDon",
      "kyHieuHoaDon",
      "soHopDong",
      "quyetDinhThauId",
      "nguonNhapKhoId",
      "hinhThucNhapXuatId",
      "csKcbChuyenGiaoId",
      "phieuDoiUngId",
    ].reduce(
      (a, item) => ({
        ...a,
        [item]: thongTinPhieu[item],
      }),
      {}
    );

    if (history.location.pathname.includes("chinh-sua")) {
      if (!thongTinForm["quyetDinhThauId"]) {
        form.setFieldsValue({ ...thongTinForm, nhapKhongTheoThau: true });
        updateData({ nhapKhongTheoThau: true });
      } else {
        form.setFieldsValue({ ...thongTinForm, nhapKhongTheoThau: false });
        updateData({ nhapKhongTheoThau: false });
      }
    } else {
      form.setFieldsValue({ ...thongTinForm });
    }
    form.setFieldsValue({
      ngayHoaDon:
        thongTinPhieu["ngayHoaDon"] && moment(thongTinPhieu["ngayHoaDon"]),
      ngayHopDong:
        thongTinPhieu["ngayHopDong"] && moment(thongTinPhieu["ngayHopDong"]),
      ngayHetHanHopDong:
        thongTinPhieu["ngayHetHanHopDong"] &&
        moment(thongTinPhieu["ngayHetHanHopDong"]),
    });
  }, [thongTinPhieu]);

  useEffect(() => {
    if (nguonNhapKhoThuocId) {
      const obj = {};
      if (khoHienTai.nhaThuoc) {
        obj.nguonNhapKhoId = nguonNhapKhoThuocId;
      }

      form.setFieldsValue(obj);
      updateData({
        thongTinPhieu: {
          ...thongTinPhieu,
          ...obj,
        },
      });
    }
  }, [khoHienTai, nguonNhapKhoThuocId]);

  useEffect(() => {
    if (hinhThucNhapXuatKhoThuocId) {
      const obj = {};
      obj.hinhThucNhapXuatId = hinhThucNhapXuatKhoThuocId;
      form.setFieldsValue(obj);
      updateData({
        thongTinPhieu: {
          ...thongTinPhieu,
          ...obj,
        },
      });
    }
  }, [khoHienTai, hinhThucNhapXuatKhoThuocId]);

  useEffect(() => {
    if (
      history.location.pathname.includes("chinh-sua") ||
      !finishMAC_DINH_TICH_NHAP_KHONG_THAU_NT ||
      dataKHONG_MAC_DINH_TICH_NHAP_KHONG_THAU_NT?.eval()
    ) {
      return;
    }
    //xử lý mặc định nhập ko theo thầu theo kho
    const obj = {
      nhapKhongTheoThau: khoHienTai.nhaThuoc,
    };
    form.setFieldsValue(obj);
    updateData({ nhapKhongTheoThau: khoHienTai.nhaThuoc });
  }, [
    khoHienTai.nhaThuoc,
    dataKHONG_MAC_DINH_TICH_NHAP_KHONG_THAU_NT,
    finishMAC_DINH_TICH_NHAP_KHONG_THAU_NT,
  ]);

  useEffect(() => {
    if (!finishCHI_HIEN_THI_THAU_CON_HIEU_LUC_KHI_NHAP_KHO) return;
    if (thongTinPhieu.khoId) {
      getByIdTongHop(thongTinPhieu.khoId).then((data) => {
        searchTongHop({
          active: true,
          trangThai: 30,
          dsLoaiDichVu: data.dsLoaiDichVu,
          size: "",
          ...(dataCHI_HIEN_THI_THAU_CON_HIEU_LUC_KHI_NHAP_KHO && {
            conHieuLuc: true,
          }),
        });
        getListTongHop({
          dsLoaiDoiTac: [20],
          active: true,
          page: "",
          size: "",
          dsLoaiDichVu: data.dsLoaiDichVu,
        });
        getListPhieuXuat({
          page: 0,
          size: 9999,
          dataSearch: {
            active: true,
            dsLoaiNhapXuat: [10],
            dsKhoId: [thongTinPhieu?.khoId],
          },
        });

        setState({
          isKhoKyGui: data?.khoKyGui || false,
        });

        if (data?.khoKyGui) {
          form.setFieldsValue({
            nhapKhongTheoThau: true,
            chuaTaoHoaDon: true,
          });
        }
      });
    } else
      searchTongHop({
        active: true,
        trangThai: 30,
        size: "",
        ...(dataCHI_HIEN_THI_THAU_CON_HIEU_LUC_KHI_NHAP_KHO && {
          conHieuLuc: true,
        }),
      });
  }, [thongTinPhieu.khoId, finishCHI_HIEN_THI_THAU_CON_HIEU_LUC_KHI_NHAP_KHO]);

  useEffect(() => {
    if (!finishCHI_HIEN_THI_THAU_CON_HIEU_LUC_KHI_NHAP_KHO) return;
    if (thongTinPhieu.nhaCungCapId) {
      if (!khoHienTai && thongTinPhieu?.khoId)
        getByIdTongHop(thongTinPhieu?.khoId).then((data) => {
          searchTongHop({
            active: true,
            trangThai: 30,
            dsLoaiDichVu: data?.dsLoaiDichVu,
            nhaCungCapId: thongTinPhieu?.nhaCungCapId,
            size: "",
            ...(dataCHI_HIEN_THI_THAU_CON_HIEU_LUC_KHI_NHAP_KHO && {
              conHieuLuc: true,
            }),
          });
        });
      else
        searchTongHop({
          active: true,
          trangThai: 30,
          dsLoaiDichVu: khoHienTai.dsLoaiDichVu,
          nhaCungCapId: thongTinPhieu?.nhaCungCapId,
          size: "",
          ...(dataCHI_HIEN_THI_THAU_CON_HIEU_LUC_KHI_NHAP_KHO && {
            conHieuLuc: true,
          }),
        });
    }
  }, [
    thongTinPhieu.nhaCungCapId,
    finishCHI_HIEN_THI_THAU_CON_HIEU_LUC_KHI_NHAP_KHO,
  ]);

  useEffect(() => {
    let _nhapKhongTheoThau = nhapKhongTheoThauForm ?? nhapKhongTheoThau;
    if (
      !isEmpty(khoHienTai) &&
      khoHienTai.hasOwnProperty("dsLoaiDichVu") &&
      typeof _nhapKhongTheoThau === "boolean"
    ) {
      let dataSearch = {
        active: true,
        dsLoaiDichVu: khoHienTai.dsLoaiDichVu,
        thau: !_nhapKhongTheoThau,
        page: "",
        size: "",
      };
      searchNguonNhapKho({ dataSearch });
    }
  }, [nhapKhongTheoThauForm, khoHienTai.dsLoaiDichVu, nhapKhongTheoThau]);

  useEffect(() => {
    getTheoTaiKhoan({
      dsLoaiKho: LOAI_KHO.NHAP_TU_NCC,
    });
    getTongHop({ active: true, page: "", size: "", dsHinhThucNhapXuat: 10 });
  }, []);

  useEffect(() => {
    if (
      isEdit &&
      thongTinPhieu.loaiNhapXuat === LOAI_NHAP_XUAT.NHAP_TU_NCC &&
      (!nhapKhongTheoThauForm || !nhapKhongTheoThau) &&
      (!ngayHopDongForm || !soHopDongForm || !ngayHetHanHopDongForm) &&
      isArray(dsNhapXuatChiTiet, true)
    ) {
      let _dataThau =
        dsNhapXuatChiTiet[dsNhapXuatChiTiet.length - 1]?.chiTietThau;

      let obj = {};
      if (!ngayHopDongForm && _dataThau?.ngayHopDong) {
        obj.ngayHopDong = moment(_dataThau?.ngayHopDong);
      }
      if (!soHopDongForm && _dataThau?.soHopDong) {
        obj.soHopDong = _dataThau?.soHopDong;
      }
      if (!ngayHetHanHopDongForm && _dataThau?.ngayHetHanHopDong) {
        obj.ngayHetHanHopDong = moment(_dataThau?.ngayHetHanHopDong);
      }
      if (!isEmpty(obj)) {
        form.setFieldsValue({ ...obj });
      }
    }
  }, [
    nhapKhongTheoThauForm,
    nhapKhongTheoThau,
    dsNhapXuatChiTiet,
    ngayHopDongForm,
    soHopDongForm,
    ngayHetHanHopDongForm,
    isEdit,
  ]);

  // kho theo nhân viên hiện tại và active
  let khoOption = useMemo(() => {
    let options = listKhoUser?.map((item, index) => (
      <Option key={index} value={item?.id}>
        {item?.ten}
      </Option>
    ));
    return options;
  }, [listKhoUser]);

  let nhaCungCapOption = useMemo(() => {
    let options = listDataTongHop?.map((item, index) => (
      <Option key={index} value={item?.id}>
        {`${item?.ma} - ${item?.ten}`}
      </Option>
    ));
    return options;
  }, [listDataTongHop]);

  const filterOption = (input = "", option) => {
    return containText(option?.props?.children, input);
  };

  useEffect(() => {
    form.setFieldsValue({ nhaCungCapId: thongTinPhieu.nhaCungCapId });
  }, [thongTinPhieu.nhaCungCapId]);

  const onChange = (type) => (e) => {
    const value = e?.hasOwnProperty("target")
      ? e?.target?.value
      : e?.hasOwnProperty("_d")
      ? moment(e._d)
      : e;

    let nguonNhapKhoId = thongTinPhieu?.nguonNhapKhoId;
    if (type == "quyetDinhThauId") {
      const goiThau = listQuyetDinhThauTongHop?.find(
        (item) => item?.id == value
      );
      if (!nhapKhongTheoThauForm) {
        nguonNhapKhoId = goiThau?.nguonNhapKho?.id ?? goiThau?.nguonNhapKhoId;
      }
      if (
        goiThau.ngayHopDong &&
        moment().isAfter(moment(goiThau?.ngayHopDong))
      ) {
        message.warning(
          `${t("kho.goiThauDaHetHopDongNgay")} ${moment(
            goiThau?.ngayHopDong
          ).format(`DD/MM/YYYY`)}`
        );
      } else if (
        !goiThau.ngayHopDong &&
        moment().isAfter(moment(goiThau?.ngayHieuLuc))
      ) {
        message.warning(
          `${t("kho.goiThauDaHetHieuLucNgay")} ${moment(
            goiThau?.ngayHieuLuc
          ).format(`DD/MM/YYYY`)}`
        );
      }
    }

    if (type == "khoId") {
      console.log("e", e);
    }
    updateData({
      thongTinPhieu: {
        ...thongTinPhieu,
        nguonNhapKhoId,
        [type]: value,
      },
    });
  };

  const onBlur = (type) => (e) => {
    const value = e?.hasOwnProperty("target")
      ? e.target.value
      : e?.hasOwnProperty("_d")
      ? moment(e._d)
      : e;
    if (type == "soHoaDon") {
      //TODO: check trung hoa don
      kiemTraSoHoaDon({
        [type]: value,
        id: thongTinPhieu?.id ? thongTinPhieu?.id : 0,
        ngayHoaDon: moment(form.getFieldValue("ngayHoaDon")).format(
          "YYYY-MM-DD"
        ),
      });
    }
    if (type == "ngayHoaDon" && e) {
      kiemTraSoHoaDon({
        [type]: moment(value).format("YYYY-MM-DD"),
        id: thongTinPhieu?.id ? thongTinPhieu?.id : 0,
        soHoaDon: thongTinPhieu?.soHoaDon,
      });
    }
  };

  const onFormValuesChange = (changedValues, allValues) => {
    let newThongTinPhieu = { ...thongTinPhieu };
    let newFields = {};
    let needUpdate = false;

    if (
      changedValues.hasOwnProperty("nhapKhongTheoThau") &&
      (allValues.nguonNhapKhoId || thongTinPhieu.nguonNhapKhoId)
    ) {
      newThongTinPhieu.nguonNhapKhoId = null;
      newFields.nguonNhapKhoId = null;
      needUpdate = true;
    }

    if (
      ["nhapKhongTheoThau", "chuaTaoHoaDon"].some((key) =>
        changedValues.hasOwnProperty(key)
      )
    ) {
      newThongTinPhieu.quyetDinhThauId = null;
      newFields.quyetDinhThauId = null;
      needUpdate = true;
    }

    if (needUpdate) {
      updateData({
        ...changedValues,
        thongTinPhieu: { ...newThongTinPhieu, ...changedValues },
      });
      form.setFieldsValue(newFields);
    }

    setIsFormChange(true);
  };

  return (
    <Main>
      <Form
        ref={formRef}
        form={form}
        layout="vertical"
        style={{ width: "100%" }}
        // onFinish={}
        onValuesChange={onFormValuesChange}
        disabled={isSuaSoLuong}
      >
        <Header1
          title={t("kho.thongTinPhieuNhap")}
          noPadding={true}
          bottom={10}
          left={0}
        />

        <Row gutter={[0, 0]}>
          <Col span={23}>
            <Item
              label={
                <div
                  className="pointer"
                  onClick={() => openInNewTab("/kho/quan-tri-kho")}
                >
                  {t("kho.khoNhap")}
                </div>
              }
              name="khoId"
              rules={[
                {
                  required: true,
                  message: `${t("khoMau.vuiLongChonKhoNhap")}!`,
                },
              ]}
              style={{ width: "100%" }}
              initialValue={thongTinPhieu?.khoId}
            >
              <Select
                allowClear
                showSearch
                disabled={thongTinPhieu?.id}
                filterOption={filterOption}
                placeholder={t("kho.chonKhoNhap")}
                onSelect={onChange("khoId")}
              >
                {khoOption}
              </Select>
            </Item>
          </Col>
          <Col span={23}>
            <Item
              label={
                <div
                  className="pointer"
                  onClick={() => openInNewTab("/danh-muc/doi-tac")}
                >
                  {t("kho.nhaCungCap")}
                </div>
              }
              name="nhaCungCapId"
              rules={
                nhapKhongTheoThau
                  ? [
                      {
                        required: true,
                        message: `${t(
                          "kho.quyetDinhThau.vuiLongChonNhaCungCap"
                        )}!`,
                      },
                    ]
                  : []
              }
              style={{ width: "100%" }}
              initialValue={thongTinPhieu?.nhaCungCapId}
            >
              <Select
                allowClear
                showSearch
                filterOption={filterOption}
                placeholder={t("kho.chonNhaCungCap")}
                onChange={onChange("nhaCungCapId")}
                disabled={!nhapKhongTheoThau}
              >
                {nhaCungCapOption}
              </Select>
            </Item>
          </Col>
          <Col span={23}>
            <Item name="nhapKhongTheoThau" valuePropName="checked">
              <Checkbox disabled={(dsNhapXuatChiTiet || []).length}>
                {t("kho.nhapKhongTheoThau")}
              </Checkbox>
            </Item>
          </Col>
          {state.isKhoKyGui && (
            <Col span={23}>
              <Item name="chuaTaoHoaDon" valuePropName="checked">
                <Checkbox>{t("kho.chuaTaoHoaDon")}</Checkbox>
              </Item>
            </Col>
          )}
          <Col span={23}>
            <Item
              label={
                <div
                  className="pointer"
                  onClick={() => openInNewTab("/kho/quan-ly-thau")}
                >
                  {t("kho.quyetDinhThau.title")}
                </div>
              }
              name="quyetDinhThauId"
              rules={[]}
              style={{ width: "100%" }}
              initialValue={thongTinPhieu?.quyetDinhThauId}
            >
              <CustomSelect
                disabled={
                  thongTinPhieu.id ||
                  nhapKhongTheoThau ||
                  (dsNhapXuatChiTiet || []).length
                }
                placeholder={
                  nhapKhongTheoThau
                    ? ""
                    : t("kho.quyetDinhThau.vuiLongChonQuyetDinhThau")
                }
                onSelect={onChange("quyetDinhThauId")}
                onClear={() => {
                  setState({ quyetDinhThauId: "", nguonNhapKhoId: "" });
                  updateData({
                    thongTinPhieu: {
                      ...thongTinPhieu,
                      quyetDinhThauId: null,
                    },
                  });
                }}
                data={listQuyetDinhThauTongHopMemo}
              />
            </Item>
          </Col>
          <Col span={23}>
            <Item
              label={
                <div
                  className="pointer"
                  onClick={() => openInNewTab("/danh-muc/nguon-nhap-kho")}
                >
                  {t("kho.nguonNhapKho")}
                </div>
              }
              name="nguonNhapKhoId"
              rules={[
                {
                  required: true,
                  message: `${t("kho.vuiLongChonNguonNhapKho")}!`,
                },
              ]}
              style={{ width: "100%" }}
              initialValue={thongTinPhieu?.nguonNhapKhoId}
            >
              <CustomSelect
                showSearch
                onChange={onChange("nguonNhapKhoId")}
                placeholder={t("kho.vuiLongChonNguonNhapKho")}
                data={listNguonNhapKho}
              />
            </Item>
          </Col>
          <Col span={23}>
            <Item
              label={
                <div
                  className="pointer"
                  onClick={() => openInNewTab("/danh-muc/hinh-thuc-nhap-xuat")}
                >
                  {t("kho.hinhThucNhap")}
                </div>
              }
              name="hinhThucNhapXuatId"
              style={{ width: "100%" }}
              initialValue={thongTinPhieu?.hinhThucNhapXuatId}
              rules={
                isBatBuocHinhThucNhap
                  ? [
                      {
                        required: true,
                        message: `${t("kho.vuiLongChonHinhThucNhap")}!`,
                      },
                    ]
                  : []
              }
            >
              <CustomSelect
                allowClear
                showSearch
                onChange={onChange("hinhThucNhapXuatId")}
                placeholder={t("kho.vuiLongChonHinhThucNhap")}
                data={listHinhThucTongHop}
              ></CustomSelect>
            </Item>
          </Col>
          <Col span={23}>
            <Item
              label={t("kho.soHoaDon")}
              name="soHoaDon"
              style={{ width: "100%" }}
              rules={
                !state.isKhoKyGui
                  ? [
                      {
                        required: true,
                        message: `${t("kho.vuiLongNhapSoHoaDon")}`,
                      },
                      {
                        whitespace: true,
                        message: `${t("kho.vuiLongNhapSoHoaDon")}`,
                      },
                    ]
                  : []
              }
              initialValue={thongTinPhieu?.soHoaDon}
            >
              <Input
                className="input-option"
                placeholder={t("kho.vuiLongNhapSoHoaDon")}
                onBlur={onBlur("soHoaDon")}
                onChange={onChange("soHoaDon")}
                disabled={
                  !checkRole([ROLES["KHO"].SUA_PHIEU_HOAN_THANH]) ||
                  isSuaSoLuong
                }
              />
            </Item>
          </Col>
          <Col span={23}>
            <Item
              label={t("kho.ngayHoaDon")}
              name="ngayHoaDon"
              style={{ width: "100%" }}
              rules={
                !state.isKhoKyGui
                  ? [
                      {
                        required: true,
                        message: `${t("kho.vuiLongChonNgayHoaDon")}!`,
                      },
                    ]
                  : []
              }
              initialValue={moment(thongTinPhieu?.ngayHoaDon)}
            >
              <DateTimePicker
                style={{ width: "100%" }}
                placeholder={t("kho.vuiLongChonNgayHoaDon")}
                format="DD / MM / YYYY"
                disabled={!checkRole([ROLES["KHO"].SUA_PHIEU_HOAN_THANH])}
                onChange={(e) => {
                  onBlur("ngayHoaDon")(e);
                  onChange("ngayHoaDon")(e);
                }}
                showTime={false}
              />
            </Item>
          </Col>
          <Col span={23}>
            <Item
              label={t("kho.kyHieuHoaDon")}
              name="kyHieuHoaDon"
              style={{ width: "100%" }}
              rules={[]}
              initialValue={thongTinPhieu?.kyHieuHoaDon}
            >
              <Input
                className="input-option"
                placeholder={t("kho.vuiLongNhapKyHieuHoaDon")}
                onChange={onChange("kyHieuHoaDon")}
              />
            </Item>
          </Col>
          <Col span={23}>
            <Item
              label={t("kho.quyetDinhThau.soHopDong")}
              name="soHopDong"
              style={{ width: "100%" }}
              rules={[]}
              initialValue={thongTinPhieu?.soHopDong}
            >
              <Input
                className="input-option"
                placeholder={t("kho.quyetDinhThau.vuiLongNhapSoHopDong")}
                onChange={onChange("soHopDong")}
              />
            </Item>
          </Col>
          <Col span={23}>
            <Item
              label={t("kho.quyetDinhThau.ngayHopDong")}
              name="ngayHopDong"
              style={{ width: "100%" }}
            >
              <DateTimePicker
                style={{ width: "100%" }}
                placeholder={t("kho.quyetDinhThau.chonNgayHopDong")}
                format="DD / MM / YYYY"
                showTime={false}
                onChange={(e) => {
                  onChange("ngayHopDong")(e);
                }}
              />
            </Item>
          </Col>
          <Col span={23}>
            <Item
              label={t("kho.quyetDinhThau.ngayHetHanHopDong")}
              name="ngayHetHanHopDong"
              style={{ width: "100%" }}
            >
              <DateTimePicker
                style={{ width: "100%" }}
                placeholder={t("kho.quyetDinhThau.chonNgayHetHanHopDong")}
                format="DD / MM / YYYY"
                showTime={false}
                onChange={(e) => {
                  onChange("ngayHetHanHopDong")(e);
                }}
              />
            </Item>
          </Col>
          <Col span={23}>
            <Item
              label={
                <div
                  className="pointer"
                  onClick={() => openInNewTab("/danh-muc/benh-vien")}
                >
                  {t("kho.csytChuyenToi")}
                </div>
              }
              name="csKcbChuyenGiaoId"
              style={{ width: "100%" }}
              initialValue={thongTinPhieu?.csKcbChuyenGiaoId}
            >
              <CustomSelect
                showSearch
                onChange={onChange("csKcbChuyenGiaoId")}
                placeholder={t("kho.vuiLongChonCsytChuyenToi")}
                data={listAllBV}
              />
            </Item>
          </Col>
          <Col span={23}>
            <Item
              label={<div className="pointer">{t("kho.chungTuHuy")}</div>}
              name="phieuDoiUngId"
              style={{ width: "100%" }}
              initialValue={thongTinPhieu?.phieuDoiUngId}
            >
              <CustomSelect
                showSearch
                onChange={onChange("phieuDoiUngId")}
                placeholder={t("kho.chonChungTuHuy")}
                data={listPhieuXuat}
                getLabel={(item) => `${item.soPhieu} - ${item.soHoaDon}`}
              />
            </Item>
          </Col>
          <Col span={23}>
            <Item
              label={t("common.ghiChu")}
              name="ghiChu"
              style={{ width: "100%" }}
              rules={[]}
              initialValue={thongTinPhieu?.ghiChu}
            >
              <Input.TextArea
                onChange={onChange("ghiChu")}
                rows={3}
                className="input-option"
                placeholder={t("kho.vuiLongNhapGhiChu")}
              />
            </Item>
          </Col>
          <Col span={23}>
            <Item
              label={t("kho.loaiNhapXuat")}
              name="loaiNhapXuat"
              rules={[
                {
                  required: true,
                  message: `${t("kho.vuiLongChonLoaiNhapXuat")}!`,
                },
              ]}
              style={{ width: "100%" }}
              initialValue={state?.loaiNhapXuat}
              hidden
            >
              <Select
                onChange={onChange("loaiNhapXuat")}
                allowClear
                showSearch
                placeholder={t("kho.vuiLongChonLoaiNhapXuat")}
              ></Select>
            </Item>
          </Col>
        </Row>
      </Form>
      {actionComponent}
    </Main>
  );
};

export default forwardRef(FormPhieuNhap);
