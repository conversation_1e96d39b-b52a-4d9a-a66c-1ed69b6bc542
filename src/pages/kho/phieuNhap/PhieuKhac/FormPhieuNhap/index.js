import React, {
  useRef,
  useState,
  useEffect,
  useMemo,
  useImperativeHandle,
  forwardRef,
} from "react";
import { Input, Form, Col, Row, Select, Menu } from "antd";
import { useDispatch, useSelector } from "react-redux";
import moment from "moment";
import { Main } from "./styled";
import { containText, openInNewTab, isArray } from "utils/index";
import Header1 from "pages/kho/components/Header1";
import {
  DatePicker,
  Select as CustomSelect,
  Button,
  Dropdown,
} from "components";
import { checkRole } from "lib-utils/role-utils";
import {
  LOAI_KHO,
  ROLES,
  THIET_LAP_CHUNG,
  TRANG_THAI_PHIEU_NHAP_XUAT,
} from "constants/index";
import { useHistory } from "react-router-dom";
import Action from "../../Action";
import { useStore, useThietLap } from "hooks";
import { SVG } from "assets";
import { useTranslation } from "react-i18next";
import { cloneDeep } from "lodash";

const { Option } = Select;
const { Item } = Form;

const FormPhieuNhap = ({ layerId, ...props }, ref) => {
  const history = useHistory();
  const { t } = useTranslation();
  const { listDataTongHop: listNguonNhapKho } = useSelector(
    (state) => state.nguonNhapKho
  );
  const { currentItem: khoHienTai, listKhoUser } = useSelector(
    (state) => state.kho
  );
  const { thongTinPhieu } = useSelector((state) => state.phieuNhapXuat);
  const { listDataTongHop } = useSelector((state) => state.doiTac);
  const listHinhThucTongHop = useStore("hinhThucNhapXuat.listTongHop", []);

  const [dataHINH_THUC_NHAP_TANG_KIEM] = useThietLap(
    THIET_LAP_CHUNG.HINH_THUC_NHAP_TANG_KIEM
  );

  const {
    quyetDinhThau: { onSearch },
    nhapKho: { kiemTraSoHoaDon },
    nguonNhapKho: { onSearchTongHop: searchNguonNhapKho },
    hinhThucNhapXuat: { getTongHop },
    kho: { getByIdTongHop, getTheoTaiKhoan },
    phieuNhapXuat: { updateData, inPhieuNhapXuat },
    doiTac: { getListTongHop },
  } = useDispatch();

  const formRef = useRef();
  const [form] = Form.useForm();
  const [state, _setState] = useState({
    ngayHoaDon: new Date(),
    loaiNhapXuat: 12,
    isKhoKyGui: false,
  });
  const setState = (data = {}) => {
    _setState((_state) => ({
      ..._state,
      ...data,
    }));
  };

  useImperativeHandle(ref, () => ({
    setData: (data) => {
      //TODO: set value form
      const thongTinForm = [
        "khoId",
        "soHoaDon",
        "kyHieuHoaDon",
        "ngayHoaDon",
        "soHopDong",
        "nguonNhapKhoId",
        "hinhThucNhapXuatId",
      ].reduce(
        (a, item) => ({
          ...a,
          [item]: data[item],
        }),
        {}
      );
      form.setFieldsValue(thongTinForm);
      setState({ id: data?.id });
    },
  }));

  const onChange = (type) => (e) => {
    const value = e?.hasOwnProperty("target")
      ? e?.target?.value
      : e?.hasOwnProperty("_d")
      ? moment(e._d)
      : e;

    let _thongTinPhieu = cloneDeep(thongTinPhieu);

    let arrHinhThucTangKiem = (dataHINH_THUC_NHAP_TANG_KIEM || "").split(",");
    if (
      type === "hinhThucNhapXuatId" &&
      isArray(arrHinhThucTangKiem, 3) &&
      !_thongTinPhieu?.id
    ) {
      let maHinhThucNhapXuat = arrHinhThucTangKiem[0];
      let maNhaCungCap = arrHinhThucTangKiem[1];
      let maNguonNhapKho = arrHinhThucTangKiem[2];

      if (maHinhThucNhapXuat) {
        let hinhThucNhapXuatId = listHinhThucTongHop.find(
          (i) => i.ma === maHinhThucNhapXuat
        )?.id;
        if (hinhThucNhapXuatId === e) {
          if (maNhaCungCap && isArray(listDataTongHop, 1)) {
            let nhaCungCapId = listDataTongHop.find(
              (i) => i.ma === maNhaCungCap
            )?.id;
            if (nhaCungCapId) {
              _thongTinPhieu.nhaCungCapId = nhaCungCapId;
            }
          }
          if (maNguonNhapKho && isArray(listNguonNhapKho, 1)) {
            let nguonNhapKhoId = listNguonNhapKho.find(
              (i) => i.ma === maNguonNhapKho
            )?.id;
            if (nguonNhapKhoId) {
              _thongTinPhieu.nguonNhapKhoId = nguonNhapKhoId;
            }
          }
        }
      }
    }

    updateData({
      thongTinPhieu: {
        ..._thongTinPhieu,
        [type]: value,
      },
    });
  };
  const onBlur = (type) => (e) => {
    const value = e?.hasOwnProperty("target")
      ? e.target.value
      : e?.hasOwnProperty("_d")
      ? moment(e._d)
      : e;
    if (type == "soHoaDon") {
      //TODO: check trung hoa don
      kiemTraSoHoaDon({
        [type]: value,
        id: thongTinPhieu?.id ? thongTinPhieu?.id : 0,
        ngayHoaDon: moment(form.getFieldValue("ngayHoaDon")).format(
          "YYYY-MM-DD"
        ),
      });
    }
    if (type == "ngayHoaDon" && e) {
      kiemTraSoHoaDon({
        [type]: moment(value).format("YYYY-MM-DD"),
        id: thongTinPhieu?.id ? thongTinPhieu?.id : 0,
        soHoaDon: thongTinPhieu?.soHoaDon,
      });
    }
  };

  const onSave = (guiDuyet) => {
    form.validateFields().then((values) => {
      props.onCreateOrUpdate({
        ...values,
        id: thongTinPhieu?.id,
        guiDuyet,
      });
    });
  };
  useEffect(() => {
    const thongTinForm = [
      "khoId",
      "soHoaDon",
      "kyHieuHoaDon",
      "soHopDong",
      "nguonNhapKhoId",
      "hinhThucNhapXuatId",
    ].reduce(
      (a, item) => ({
        ...a,
        [item]: thongTinPhieu[item],
      }),
      {}
    );

    form.setFieldsValue({
      ...thongTinForm,
      ngayHoaDon:
        thongTinPhieu["ngayHoaDon"] && moment(thongTinPhieu["ngayHoaDon"]),
      thoiGianDuyet:
        thongTinPhieu["thoiGianDuyet"] &&
        moment(thongTinPhieu["thoiGianDuyet"]),
    });
  }, [thongTinPhieu]);

  useEffect(() => {
    if (thongTinPhieu.khoId) {
      getByIdTongHop(thongTinPhieu.khoId).then((data) => {
        onSearch({
          dataSearch: {
            active: true,
            trangThai: 30,
            dsLoaiDichVu: data.dsLoaiDichVu,
          },
        });
        getListTongHop({
          dsLoaiDoiTac: [20],
          active: true,
          page: "",
          size: "",
          dsLoaiDichVu: data.dsLoaiDichVu,
        });

        setState({
          isKhoKyGui: data?.khoKyGui || false,
        });

        if (data?.khoKyGui) {
          form.setFieldsValue({
            // nhapKhongTheoThau: true,
            chuaTaoHoaDon: true,
          });
        }
      });
    } else onSearch({ dataSearch: { active: true, trangThai: 30 } });
  }, [thongTinPhieu.khoId]);

  useEffect(() => {
    if (thongTinPhieu.nhaCungCapId) {
      if (!khoHienTai && thongTinPhieu?.khoId)
        getByIdTongHop(thongTinPhieu?.khoId).then((data) => {
          onSearch({
            dataSearch: {
              active: true,
              trangThai: 30,
              dsLoaiDichVu: data?.dsLoaiDichVu,
              nhaCungCapId: thongTinPhieu?.nhaCungCapId,
            },
          });
        });
      else
        onSearch({
          dataSearch: {
            active: true,
            trangThai: 30,
            dsLoaiDichVu: khoHienTai?.dsLoaiDichVu,
            nhaCungCapId: thongTinPhieu?.nhaCungCapId,
          },
        });
    }
  }, [thongTinPhieu.nhaCungCapId]);

  useEffect(() => {
    if (khoHienTai) {
      let dataSearch = {
        active: true,
        dsLoaiDichVu: khoHienTai?.dsLoaiDichVu,
        page: "",
        size: "",
        thau: false,
      };
      searchNguonNhapKho({ dataSearch });
    }
  }, [khoHienTai]);
  useEffect(() => {
    getTheoTaiKhoan({
      dsLoaiKho: LOAI_KHO.NHAP_TU_NCC,
    });
    getTongHop({ active: true, page: "", size: "", dsHinhThucNhapXuat: 10 });
  }, []);

  // kho theo nhân viên hiện tại và active
  let khoOption = useMemo(() => {
    let options = listKhoUser?.map((item, index) => (
      <Option key={index} value={item?.id}>
        {item?.ten}
      </Option>
    ));
    return options;
  }, [listKhoUser]);

  let nhaCungCapOption = useMemo(() => {
    let options = listDataTongHop?.map((item, index) => (
      <Option key={index} value={item?.id}>
        {`${item?.ma} - ${item?.ten}`}
      </Option>
    ));
    return options;
  }, [listDataTongHop]);

  const filterOption = (input = "", option) => {
    return containText(option?.props?.children, input);
  };

  useEffect(() => {
    form.setFieldsValue({ nhaCungCapId: thongTinPhieu.nhaCungCapId });
  }, [thongTinPhieu.nhaCungCapId]);

  const onPrint = () => {
    if (thongTinPhieu?.id) {
      inPhieuNhapXuat({ dsId: [thongTinPhieu?.id] });
    }
  };

  const menu = (
    <Menu
      items={[
        {
          key: 0,
          label: (
            <a href={() => false} onClick={onPrint}>
              {t("kho.inPhieuNhapKho")}
            </a>
          ),
        },
      ]}
    />
  );

  const canShowSaveButton = useMemo(() => {
    if (!thongTinPhieu?.id) {
      return !checkRole([ROLES["KHO"].GUI_DUYET_PHIEU_NHAP_KHO]);
    }
    if (thongTinPhieu?.trangThai === TRANG_THAI_PHIEU_NHAP_XUAT.HOAN_THANH) {
      return checkRole([ROLES["KHO"].SUA_PHIEU_HOAN_THANH]);
    }
    return checkRole([ROLES["KHO"].SUA_PHIEU_NHAP_KHO]);
  }, [thongTinPhieu]);

  return (
    <Main>
      <Form
        ref={formRef}
        form={form}
        layout="vertical"
        style={{ width: "100%" }}
      >
        <Header1
          title={t("kho.thongTinPhieuNhapKhac")}
          noPadding={true}
          bottom={10}
          left={0}
        ></Header1>

        <Row gutter={[0, 0]}>
          <Col span={23}>
            <Item
              label={
                <div
                  className="pointer"
                  onClick={() => openInNewTab("/kho/quan-tri-kho")}
                >
                  {t("kho.khoNhap")}
                </div>
              }
              name="khoId"
              rules={[
                {
                  required: true,
                  message: t("baoCao.vuiLongChonKhoNhap"),
                },
              ]}
              style={{ width: "100%" }}
              initialValue={thongTinPhieu?.khoId}
            >
              <Select
                allowClear
                showSearch
                disabled={thongTinPhieu?.id}
                filterOption={filterOption}
                placeholder={t("kho.chonKhoNhap")}
                onSelect={onChange("khoId")}
              >
                {khoOption}
              </Select>
            </Item>
          </Col>
          <Col span={23}>
            <Item
              label={
                <div
                  className="pointer"
                  onClick={() => openInNewTab("/danh-muc/doi-tac")}
                >
                  {t("kho.nhaCungCap")}
                </div>
              }
              name="nhaCungCapId"
              rules={[
                {
                  required: true,
                  message: t("kho.vuiLongChonNhaCungCap"),
                },
              ]}
              style={{ width: "100%" }}
              initialValue={thongTinPhieu?.nhaCungCapId}
            >
              <Select
                allowClear
                showSearch
                filterOption={filterOption}
                placeholder={t("kho.chonNhaCungCap")}
                onChange={onChange("nhaCungCapId")}
              >
                {nhaCungCapOption}
              </Select>
            </Item>
          </Col>
          <Col span={23}>
            <Item
              label={
                <div
                  className="pointer"
                  onClick={() => openInNewTab("/danh-muc/nguon-nhap-kho")}
                >
                  {t("kho.nguonNhapKho")}
                </div>
              }
              name="nguonNhapKhoId"
              rules={[
                {
                  required: true,
                  message: t("kho.vuiLongChonNguonNhapKho"),
                },
              ]}
              style={{ width: "100%" }}
              initialValue={thongTinPhieu?.nguonNhapKhoId}
            >
              <CustomSelect
                allowClear
                showSearch
                onChange={onChange("nguonNhapKhoId")}
                placeholder={t("kho.vuiLongChonNguonNhapKho")}
                data={listNguonNhapKho}
              ></CustomSelect>
            </Item>
          </Col>
          <Col span={23}>
            <Item
              label={
                <div
                  className="pointer"
                  onClick={() => openInNewTab("/danh-muc/hinh-thuc-nhap-xuat")}
                >
                  {t("kho.hinhThucNhap")}
                </div>
              }
              name="hinhThucNhapXuatId"
              style={{ width: "100%" }}
              initialValue={thongTinPhieu?.hinhThucNhapXuatId}
            >
              <CustomSelect
                allowClear
                showSearch
                onChange={onChange("hinhThucNhapXuatId")}
                placeholder={t("kho.vuiLongChonHinhThucNhap")}
                data={listHinhThucTongHop}
              ></CustomSelect>
            </Item>
          </Col>

          <Col span={23}>
            <Item
              label={t("kho.soHoaDon")}
              name="soHoaDon"
              style={{ width: "100%" }}
              rules={
                !state.isKhoKyGui &&
                !history.location.pathname.includes("phieu-nhap-khac")
                  ? [
                      {
                        required: true,
                        message: t("kho.vuiLongNhapSoHoaDon"),
                      },
                      {
                        whitespace: true,
                        message: t("kho.vuiLongNhapSoHoaDon"),
                      },
                    ]
                  : []
              }
              initialValue={thongTinPhieu?.soHoaDon}
            >
              <Input
                className="input-option"
                placeholder={t("kho.vuiLongNhapSoHoaDon")}
                onBlur={onBlur("soHoaDon")}
                onChange={onChange("soHoaDon")}
                disabled={!checkRole([ROLES["KHO"].SUA_PHIEU_HOAN_THANH])}
              />
            </Item>
          </Col>
          <Col span={23}>
            <Item
              label={t("kho.ngayHoaDon")}
              name="ngayHoaDon"
              style={{ width: "100%" }}
              rules={
                !state.isKhoKyGui &&
                !history.location.pathname.includes("phieu-nhap-khac")
                  ? [
                      {
                        required: true,
                        message: t("kho.vuiLongChonNgayHoaDon"),
                      },
                    ]
                  : []
              }
              initialValue={moment(thongTinPhieu?.ngayHoaDon)}
            >
              <DatePicker
                style={{ width: "100%" }}
                placeholder={t("kho.vuiLongChonNgayHoaDon")}
                format="DD / MM / YYYY"
                disabled={!checkRole([ROLES["KHO"].SUA_PHIEU_HOAN_THANH])}
                onChange={(e) => {
                  onBlur("ngayHoaDon")(e);
                  onChange("ngayHoaDon")(e);
                }}
              />
            </Item>
          </Col>

          <Col span={23}>
            <Item
              label={t("kho.kyHieuHoaDon")}
              name="kyHieuHoaDon"
              style={{ width: "100%" }}
              rules={[]}
              initialValue={thongTinPhieu?.kyHieuHoaDon}
            >
              <Input
                className="input-option"
                placeholder={t("kho.vuiLongNhapKyHieuHoaDon")}
                onChange={onChange("kyHieuHoaDon")}
              />
            </Item>
          </Col>
          <Col span={23}>
            <Item
              label={t("kho.soHopDong")}
              name="soHopDong"
              style={{ width: "100%" }}
              rules={[]}
              initialValue={thongTinPhieu?.soHopDong}
            >
              <Input
                className="input-option"
                placeholder={t("kho.vuiLongNhapSoHopDong")}
                onChange={onChange("soHopDong")}
              />
            </Item>
          </Col>
          {window.location.pathname.includes(
            "/kho/phieu-nhap-khac/chi-tiet/"
          ) && (
            <>
              <Col span={23}>
                <Item
                  label={t("common.soPhieu")}
                  name="soPhieu"
                  style={{ width: "100%" }}
                  rules={[]}
                  initialValue={thongTinPhieu?.soPhieu}
                >
                  <Input className="input-option" disabled />
                </Item>
              </Col>
              <Col span={23}>
                <Item
                  label={t("kho.thoiGianDuyet")}
                  name="thoiGianDuyet"
                  style={{ width: "100%" }}
                  initialValue={
                    thongTinPhieu?.thoiGianDuyet
                      ? moment(thongTinPhieu?.thoiGianDuyet)
                      : null
                  }
                >
                  <DatePicker
                    style={{ width: "100%" }}
                    format="DD / MM / YYYY HH:mm:ss"
                    className="date-option"
                    disabled={
                      !checkRole([ROLES["KHO"].SUA_THOI_GIAN_DUYET_NGUOI_DUYET])
                    }
                  />
                </Item>
              </Col>
            </>
          )}
          <Col span={23}>
            <Item
              label={t("kho.ghiChu")}
              name="ghiChu"
              style={{ width: "100%" }}
              rules={[]}
              initialValue={thongTinPhieu?.ghiChu}
            >
              <Input.TextArea
                onChange={onChange("ghiChu")}
                rows={3}
                className="input-option"
                placeholder={t("kho.vuiLongNhapGhiChu")}
              />
            </Item>
          </Col>
          <Col span={23}>
            <Item
              label={t("kho.loaiNhapXuat")}
              name="loaiNhapXuat"
              rules={[
                {
                  required: true,
                  message: t("kho.vuiLongChonLoaiNhapXuat"),
                },
              ]}
              style={{ width: "100%" }}
              initialValue={state?.loaiNhapXuat}
              hidden
            >
              <Select
                onChange={onChange("loaiNhapXuat")}
                allowClear
                showSearch
                placeholder={t("kho.vuiLongChonLoaiNhapXuat")}
              ></Select>
            </Item>
          </Col>
        </Row>
      </Form>

      {window.location.pathname.indexOf("/chinh-sua") > -1 ? (
        <div className="action">
          {checkRole([ROLES["KHO"].GUI_DUYET_PHIEU_NHAP_KHO]) && (
            <Button
              onClick={() => onSave(true)}
              rightIcon={<SVG.IcGuiVaDuyet />}
            >
              {t("kho.luuVaGuiDuyet")}
            </Button>
          )}
          {!checkRole([ROLES["KHO"].GUI_DUYET_PHIEU_NHAP_KHO]) && (
            <Button
              onClick={() => onSave(false)}
              type={"primary"}
              minWidth={120}
              rightIcon={<SVG.IcSave />}
            >
              {t("common.luu")}
            </Button>
          )}
        </div>
      ) : (
        <Action
          layerId={layerId}
          otherBtn={
            <Dropdown overlay={menu} trigger={["click"]} placement="top">
              <Button rightIcon={<SVG.IcPrint />} minWidth={80}>
                {t("common.inGiayTo")}
              </Button>
            </Dropdown>
          }
          onSave={onSave}
          otherRightBtn={
            canShowSaveButton && (
              <Button
                onClick={() => onSave(false)}
                type={"primary"}
                minWidth={120}
                rightIcon={<SVG.IcSave />}
              >
                {t("common.luu")}
              </Button>
            )
          }
        />
      )}
    </Main>
  );
};

export default forwardRef(FormPhieuNhap);
