import { NV_THOI_GIAN_LAM_VIEC } from "client/api";

import apiBase from "./api-base";

export default {
  ...apiBase.init({ API: NV_THOI_GIAN_LAM_VIEC }),
  postImport: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NV_THOI_GIAN_LAM_VIEC}/import`, payload, {
          responseType: "arraybuffer",
        })
        .then((s) => {
          if (s.data?.code == 0) resolve(s.data);
          else reject(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
};
