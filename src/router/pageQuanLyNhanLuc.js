import React from "react";
import { ROLES } from "constants/index";
import { Page } from "pages/constants";

const SubPageQuanLyNhanLuc = React.lazy(() =>
  import("pages/home/<USER>/QuanLyNhanLuc")
);
const DanhSachQuanLyNhanLucHangNgay = React.lazy(() =>
  import("pages/quanLyNhanLuc/DanhSachQuanLyNhanLucHangNgay")
);
const ChiTietQuanLyNhanLucHangNgay = React.lazy(() =>
  import("pages/quanLyNhanLuc/ChiTietQuanLyNhanLucHangNgay")
);
const DanhSachThongTinLamViecNhanVien = React.lazy(() =>
  import("pages/quanLyNhanLuc/DanhSachThongTinLamViecNhanVien")
);

export default {
  SubPageQuanLyNhanLuc: {
    component: Page(SubPageQuanLyNhanLuc, [ROLES["QUAN_LY_NHAN_LUC"].MODULE]),
    accessRoles: [ROLES["QUAN_LY_NHAN_LUC"].MODULE],
    path: "/quan-ly-nhan-luc",
    exact: true,
  },
  DanhSachQuanLyNhanLucHangNgay: {
    component: Page(DanhSachQuanLyNhanLucHangNgay, [
      ROLES["QUAN_LY_NHAN_LUC"].QUAN_LY_NHAN_LUC,
    ]),
    accessRoles: [ROLES["QUAN_LY_NHAN_LUC"].QUAN_LY_NHAN_LUC],
    path: "/quan-ly-nhan-luc/danh-sach-quan-ly-nhan-luc-hang-ngay",
    exact: true,
  },
  ChiTietQuanLyNhanLucHangNgay: {
    component: Page(ChiTietQuanLyNhanLucHangNgay, [
      ROLES["QUAN_LY_NHAN_LUC"].QUAN_LY_NHAN_LUC,
    ]),
    accessRoles: [ROLES["QUAN_LY_NHAN_LUC"].QUAN_LY_NHAN_LUC],
    path: "/quan-ly-nhan-luc/chi-tiet-quan-ly-nhan-luc-hang-ngay/:id",
    exact: true,
  },
  danhSachThongTinLamViecNhanVien: {
    component: Page(DanhSachThongTinLamViecNhanVien, [
      ROLES["QUAN_LY_NHAN_LUC"].DANH_SACH_THONG_TIN_LAM_VIEC_NHAN_VIEN,
    ]),
    accessRoles: [],
    path: "/quan-ly-nhan-luc/danh-sach-thong-tin-lam-viec-nhan-vien",
    exact: true,
  },
};
